import type { AuditStatusType } from '#/types/materialManagement';

import { ecostServiceRequestClient } from '#/api/request';

export const MaterialReceiptStatus = {
  UN_RECEIVED: 'UN_RECEIVED',
  RECEIVED: 'RECEIVED',
  PARTIAL_RECEIVED: 'PARTIAL_RECEIVED',
} as const;
export type MaterialReceiptStatusEnum =
  (typeof MaterialReceiptStatus)[keyof typeof MaterialReceiptStatus];

export const MaterialType = {
  CONSUME_MATERIAL: 'CONSUME_MATERIAL',
  CONCRETE: 'CONCRETE',
  TURNOVERME_MATERIAL: 'TURNOVERME_MATERIAL',
  FIXEDASSETSL_CONSUMABLES: 'FIXEDASSETSL_CONSUMABLES',
} as const;
export type MaterialTypeEnmu = (typeof MaterialType)[keyof typeof MaterialType];

export const MaterialSearchType = {
  CONTRACT: 'CONTRACT',
  MATERIAL_DICT: 'MATERIAL_DICT',
} as const;
export type MaterialSearchTypeEnum =
  (typeof MaterialSearchType)[keyof typeof MaterialSearchType];

/**
 * 退货单 -- 获取时间筛选列表
 *
 */
export function getTimeList() {
  return ecostServiceRequestClient.get(`/return-inventory-form/date-tree`);
}
/**
 * 退货单 -- 获取单据列表
 */
interface getInspectionBillListType {
  year?: number;
  month?: number;
  day?: number;
}
export function getInspectionBillList(params?: getInspectionBillListType) {
  return ecostServiceRequestClient.get(`/return-inventory-form/form`, {
    params,
  });
}
/**
 * 退库单 -- 新增退库单
 */
interface addInspectionBillListType {
  returnInventoryType?: string;
}
export function addInspectionBillList(data: addInspectionBillListType) {
  return ecostServiceRequestClient.post(`/return-inventory-form/form`, data);
}
/**
 * 退库单 -- 退库单供应商
 */
export function getSupplierList() {
  return ecostServiceRequestClient.get(`/return-inventory-form/supplier-list`);
}
/**
 * 退库单 -- 编辑退库单
 */
interface editInspectionBillType {
  supplierId?: string;
  supplierName?: string;
  year?: number;
  month?: number;
  day?: number;
}
export function editInspectionBill(id: string, data: editInspectionBillType) {
  return ecostServiceRequestClient.patch(
    `/return-inventory-form/form/${id}`,
    data,
  );
}
/**
 * 退库单 -- 删除退库单
 */
export function delInspectionBill(id: string) {
  return ecostServiceRequestClient.delete(`/return-inventory-form/form/${id}`);
}
/**
 * 退库单 -- 变更提交状态
 */
export function changeSubmitStatus(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/return-inventory-form/submit/${id}`,
    data,
  );
}
/**
 * 退库单 -- 变更审核状态
 */
export function changeAuditStatus(id: string, auditStatus: AuditStatusType) {
  const data = {
    auditStatus,
  };
  return ecostServiceRequestClient.patch(
    `/return-inventory-form/audit/${id}`,
    data,
  );
}
/**
 * 退库单 -- 获取退库单明细
 */
export function getInspectionDetailList(id: string) {
  return ecostServiceRequestClient.get(`/return-inventory-form/detail/${id}`);
}
/**
 * 退库单 -- 新增退库单明细
 */
export function addInspectionDetail(data: any) {
  return ecostServiceRequestClient.post(
    `/return-inventory-form/detail/add`,
    data,
  );
}
/**
 * 退库单 -- 获取可选材料分类
 */
interface getMaterialCategoryListType {
  materialReversalId?: string;
}
export function getMaterialCategoryList(params?: getMaterialCategoryListType) {
  return ecostServiceRequestClient.get(
    `/return-inventory-form/choose/materialCategory`,
    {
      params,
    },
  );
}
/**
 * 退库单 -- 获取可选材料明细
 */
interface getMaterialDetailListType {
  materialReversalId?: string;
  categoryId?: string;
}
export function getMaterialDetailList(params: getMaterialDetailListType) {
  return ecostServiceRequestClient.get(
    `/return-inventory-form/choose/materialDetails`,
    {
      params,
    },
  );
}
/**
 * 退库单 -- 修改退库数量
 */
export function editReturnInventoryFormMaterialDetail(id: string, data: any) {
  return ecostServiceRequestClient.patch(`/return-inventory-form/${id}`, data);
}
/**
 * 退库单 -- 获取附件列表
 */
export function getAttachmentList(materialReversalId: string) {
  return ecostServiceRequestClient.get(
    `/return-inventory-form/attachment/${materialReversalId}`,
  );
}
/**
 * 退库单 -- 添加附件
 */
export function addAttachment(data: any) {
  return ecostServiceRequestClient.post(
    `/return-inventory-form/attachment`,
    data,
  );
}
/**
 * 退库单 -- 删除附件
 */
export function delAttachmen(id: string) {
  return ecostServiceRequestClient.delete(
    `/return-inventory-form/attachment/${id}`,
  );
}
/**
 * 退库单 -- 删除退库单明细
 */
export function delInspectionDetail(id: string) {
  return ecostServiceRequestClient.delete(
    `/return-inventory-form/detail/${id}`,
  );
}
/**
 * 退库单 -- 删除退库单上移下移
 */
interface moveInspectionDetailType {
  fromId: string;
  toId: string;
}
export function moveInspectionDetail(params: moveInspectionDetailType) {
  return ecostServiceRequestClient.post(
    `/return-inventory-form/move`,
    {},
    {
      params,
    },
  );
}
