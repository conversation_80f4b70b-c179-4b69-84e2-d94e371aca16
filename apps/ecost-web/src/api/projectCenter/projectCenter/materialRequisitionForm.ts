import { ecostServiceRequestClient } from '#/api/request';

// 获取时间选择列表
export function getTimeList() {
  return ecostServiceRequestClient.get(`/material-requisition-form/time-list`);
}

// 获取单据列表
export function getBillList(params: any) {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/requisition-bill/list`,
    { params },
  );
}

// 新增领料单
export function addRequisitionBill() {
  return ecostServiceRequestClient.post(
    `/material-requisition-form/requisition-bill/_add`,
  );
}

// 获取可选的领料单位
export function getRequisitionDepartmentList() {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/requisition-bill/requisition-department/list`,
  );
}

// 修改领料单
export function editRequisitionBill(data: any) {
  return ecostServiceRequestClient.post(
    `/material-requisition-form/requisition-bill/_edit`,
    data,
  );
}

// 删除领料单
export function deleteRequisitionBill(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-requisition-form/requisition-bill/${id}`,
  );
}

// 获取领料单明细
export function getRequisitionDetailList(id: string) {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/${id}/detail/list`,
  );
}

// 获取可选择的材料字典分类
export function getMaterialCategoryList(id: string) {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/${id}/detail/material-category/list`,
  );
}

// 获取可选择的材料明细
export function getMaterialDetailList(
  requisitionFormId: string,
  categoryId: string,
) {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/${requisitionFormId}/detail/material-detail/list`,
    { params: { categoryId } },
  );
}

// 新增领料单明细
export function addRequisitionDetail(requisitionFormId: string, data: any) {
  return ecostServiceRequestClient.post(
    `/material-requisition-form/${requisitionFormId}/detail/_add`,
    data,
  );
}

// 删除领料单明细
export function deleteRequisitionDetail(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-requisition-form/requisition-detail/${id}`,
  );
}

// 编辑领料单明细
export function editRequisitionDetail(data: any) {
  return ecostServiceRequestClient.post(
    `/material-requisition-form/requisition-detail/_edit`,
    data,
  );
}

// 领料单明细上移下移
export function moveRequisitionDetail({
  fromId,
  toId,
}: {
  fromId: string;
  toId: string;
}) {
  return ecostServiceRequestClient.post(
    `/material-requisition-form/requisition-detail/_move`,
    {},
    { params: { fromId, toId } },
  );
}

// 领料单附件上传
export function addRequisitionAttachment(data: any) {
  return ecostServiceRequestClient.post(
    `/material-requisition-form/requisition-bill/attachment`,
    data,
  );
}

// 获取领料单附件列表
export function getRequisitionAttachmentList(requisitionBillId: string) {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/${requisitionBillId}/attachment/list`,
  );
}

// 领料单附件删除
export function delRequisitionAttachment(id: string) {
  return ecostServiceRequestClient.delete(
    `/material-requisition-form/requisition-bill/attachment/${id}`,
  );
}

// 获取业务成本科目
export function getBusinessCostSubjectList(requisitionBillId: string) {
  return ecostServiceRequestClient.get(
    `/material-requisition-form/${requisitionBillId}/business-cost-subject/list`,
  );
}
