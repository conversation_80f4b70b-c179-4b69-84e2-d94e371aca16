import type { Router } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';
import { startProgress, stopProgress } from '@vben/utils';

import { accessRoutes, coreRouteNames } from '#/router/routes';
import { useAuthStore } from '#/store';

import { generateAccess, getRouteContext } from './access';

/**
 * 通用守卫配置
 * @param router
 */
function setupCommonGuard(router: Router) {
  // 记录已经加载的页面
  const loadedPaths = new Set<string>();

  router.beforeEach(async (to) => {
    to.meta.loaded = loadedPaths.has(to.path);

    // 页面加载进度条
    if (!to.meta.loaded && preferences.transition.progress) {
      startProgress();
    }
    return true;
  });

  router.afterEach((to) => {
    // 记录页面是否加载,如果已经加载，后续的页面切换动画等效果不在重复执行

    loadedPaths.add(to.path);

    // 关闭页面加载进度条
    if (preferences.transition.progress) {
      stopProgress();
    }
  });
}

/**
 * 权限访问守卫配置
 * @param router
 */
function setupAccessGuard(router: Router) {
  router.beforeEach(async (to, from) => {
    const accessStore = useAccessStore();
    const userStore = useUserStore();
    const authStore = useAuthStore();

    // 基本路由，这些路由不需要进入权限拦截
    if (coreRouteNames.includes(to.name as string)) {
      if (to.path === LOGIN_PATH && accessStore.accessToken) {
        return decodeURIComponent(
          (to.query?.redirect as string) ||
            userStore.userInfo?.homePath ||
            DEFAULT_HOME_PATH,
        );
      }
      return true;
    }

    // accessToken 检查
    if (!accessStore.accessToken) {
      // 明确声明忽略权限访问权限，则可以访问
      if (to.meta.ignoreAccess) {
        return true;
      }

      // 没有访问权限，跳转登录页面
      if (to.fullPath !== LOGIN_PATH) {
        return {
          path: LOGIN_PATH,
          // 如不需要，直接删除 query
          // query:
          //   to.fullPath === DEFAULT_HOME_PATH
          //     ? {}
          //     : { redirect: encodeURIComponent(to.fullPath) },
          // 携带当前跳转的页面，登录后重新跳转该页面
          replace: true,
        };
      }
      return to;
    }

    // 判断 url query 参数中的路由上下文和 sessionStorage 中是否一致
    // 如果不一致，以 sessionStorage 中保存的为准，并且替换 href
    const sessionProductCode = sessionStorage.getItem('productCode');
    const sessionOrgType = sessionStorage.getItem('x-org-type');
    const sessionOrgId = sessionStorage.getItem('x-org-id');
    const hasSessionParams =
      sessionProductCode && sessionOrgType && sessionOrgId;
    if (hasSessionParams) {
      // 如果 to route 缺少 query 上下文，则补全 to route query 上下文
      if (!to.query.productCode || !to.query.orgType || !to.query.orgId) {
        await router.replace({
          path: to.path,
          query: {
            ...to.query,
            productCode: sessionProductCode,
            orgType: sessionOrgType,
            orgId: sessionOrgId,
          },
        });
      }
      // 如果当前 URL 路由上下文信息和 sessionStorage 保存的不一致
      // 替换当前 URL 并刷新页面
      const routeCtx = getRouteContext();
      const currentRouteProjectCode =
        (to.query.productCode as string) || routeCtx.productCode;
      const currentRouteOrgType =
        (to.query.orgType as string) || routeCtx.orgType;
      const currentRouteOrgId = (to.query.orgId as string) || routeCtx.orgId;
      if (
        currentRouteProjectCode !== sessionProductCode ||
        currentRouteOrgType !== sessionOrgType ||
        currentRouteOrgId !== sessionOrgId
      ) {
        const url = window.location.href;
        const queryString = url.split('?')[1];

        const queryParams = new URLSearchParams(queryString);
        queryParams.set('productCode', sessionProductCode);
        queryParams.set('orgType', sessionOrgType);
        queryParams.set('orgId', sessionOrgId);

        const { hash, origin } = window.location;
        const newUrl = `${origin}${hash ? '/#' : ''}${to.path}?${queryParams.toString()}`;

        window.location.href = newUrl;
        // 如果是 hash 模式，则需要强制刷新页面
        if (hash) {
          window.location.reload();
        }

        return true;
      }
    }

    // 是否已经生成过动态路由
    if (accessStore.isAccessChecked) {
      return true;
    }

    // 生成路由表
    // 当前登录用户拥有的角色标识列表
    const userInfo = userStore.userInfo || (await authStore.fetchUserInfo());
    const userRoles = userInfo?.roles ?? [];

    // 生成菜单和路由
    const { accessibleMenus, accessibleRoutes } = await generateAccess({
      roles: userRoles,
      router,
      // 则会在菜单中显示，但是访问会被重定向到403
      routes: accessRoutes,
    });

    // 获取权限信息

    // 保存菜单信息和路由信息
    accessStore.setAccessMenus(accessibleMenus);
    accessStore.setAccessRoutes(accessibleRoutes);
    accessStore.setIsAccessChecked(true);
    const redirectPath = (from.query.redirect ??
      (to.path === DEFAULT_HOME_PATH
        ? userInfo?.homePath || DEFAULT_HOME_PATH
        : to.fullPath)) as string;

    return {
      ...router.resolve(decodeURIComponent(redirectPath)),
      replace: true,
    };
  });
}

/**
 * 项目守卫配置
 * @param router
 */
function createRouterGuard(router: Router) {
  /** 通用 */
  setupCommonGuard(router);
  /** 权限访问 */
  setupAccessGuard(router);
}

export { createRouterGuard };
