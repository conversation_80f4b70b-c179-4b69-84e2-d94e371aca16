import type {
  ComponentRecordType,
  GenerateMenuAndRoutesOptions,
} from '@vben/types';

import { generateAccessible } from '@vben/access';
import { preferences } from '@vben/preferences';

import { ElMessage } from 'element-plus';

import { getAllMenusApi } from '#/api';
import { BasicLayout, IFrameView } from '#/layouts';
import { $t } from '#/locales';

/**
 * 获取 URL 路由上下文信息：产品编码、组织id等
 */
function getRouteContext() {
  const url = window.location.href;
  const queryString = url.split('?')[1];
  if (!queryString) {
    return {
      productCode: null,
      orgType: null,
      orgId: null,
    };
  }

  const params = new URLSearchParams(queryString);

  return {
    productCode: params.get('productCode'),
    orgType: params.get('orgType'),
    orgId: params.get('orgId'),
  };
}

const forbiddenComponent = () => import('#/views/_core/fallback/forbidden.vue');
async function generateAccess(options: GenerateMenuAndRoutesOptions) {
  const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');

  const { productCode, orgType, orgId } = getRouteContext();

  const layoutMap: ComponentRecordType = {
    BasicLayout,
    IFrameView,
  };

  return await generateAccessible(preferences.app.accessMode, {
    ...options,
    fetchMenuListAsync: async () => {
      ElMessage({
        duration: 1500,
        message: `${$t('common.loadingMenu')}...`,
      });

      // 存储
      if (productCode) {
        sessionStorage.setItem('productCode', productCode as string);
      }
      if (orgType) {
        sessionStorage.setItem('x-org-type', orgType as string);
      }
      if (orgId) {
        sessionStorage.setItem('x-org-id', orgId as string);
      }
      return await getAllMenusApi({
        productCode: sessionStorage.getItem('productCode'),
      });
    },
    // 可以指定没有权限跳转403页面
    forbiddenComponent,
    // 如果 route.meta.menuVisibleWithForbidden = true
    layoutMap,
    pageMap,
  });
}

export { generateAccess, getRouteContext };
