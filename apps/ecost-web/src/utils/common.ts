import _ from 'lodash';

// 展开按钮数据
export const expandBtnList = [
  { id: 0, iconName: 'ph:selection-all', title: '展开全部' },
  // { id: -1, iconName: 'ph:selection-all-fill', title: '折叠全部' },
  { id: 1, iconName: 'icon-park-outline:one-key', title: '展开第一层' },
  { id: 2, iconName: 'icon-park-outline:two-key', title: '展开第二层' },
  { id: 3, iconName: 'icon-park-outline:three-key', title: '展开第三层' },
];

/**
 *
 * @param tree 树及数据结构
 * @param level 需要展开的层级
 * @returns {string[]} 返回符合层级的id
 */

export function getIdsByLevel(tree: any[], level: number): string[] {
  if (!tree || tree.length === 0) return [];

  // 如果是0，返回所有id
  if (level === 0) {
    const allIds: string[] = [];
    const getAllIds = (nodes: any[]) => {
      nodes.forEach((node) => {
        allIds.push(node.id);
        if (node.children && node.children.length > 0) {
          getAllIds(node.children);
        }
      });
    };
    getAllIds(tree);
    return allIds;
  }

  // 获取指定层级的id
  let currentLevel = 1;
  let currentNodes = [...tree];
  let result: string[] = [];

  while (currentLevel <= level && currentNodes.length > 0) {
    if (currentLevel === level) {
      result = currentNodes.map((node) => node.id);
      break;
    }

    const nextNodes: any[] = [];
    currentNodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        nextNodes.push(...node.children);
      }
    });

    currentNodes = nextNodes;
    currentLevel++;
  }

  return result;
}

/**
 *
 * @param tree 数据结构
 * @param targetLevel 目标层级
 * @param currentLevel 当前层级
 * @param parentValues 父级节点值数组
 * @returns {any[]} 返回所有到达目标层级的路径，每个路径都是一个数组
 */

export const finLeaveKeys = (
  tree: any,
  targetLevel: number,
  currentLevel = 1,
  parentValues: any[] = [],
): any[] => {
  if (!tree || tree.length === 0) return [];

  let result: any[] = [];

  // 递归到目标层级时，返回该层级的路径，并包括所有父级节点
  if (currentLevel === targetLevel) {
    result = tree.map((node: any) => [...parentValues, node.value]); // 包含父级节点与当前节点
  } else {
    // 如果还没到目标层级，继续递归
    for (const node of tree) {
      result = result.concat(
        finLeaveKeys(
          node.children || [node], // 如果没有children，直接处理该节点
          targetLevel,
          currentLevel + 1,
          [...parentValues, node.value], // 递归时保持父级值
        ),
      );
    }
  }

  return _.union(result.flat());
};

/**
 * 将数字转换为中文数字（仅支持正整数）
 * @param num - 要转换的数字
 * @returns 中文数字字符串
 */
export function numberToChinese(num: number): string {
  const chineseDigits = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
  ];
  const chineseUnits = ['', '十', '百', '千', '万'];

  if (num < 0 || !Number.isInteger(num)) {
    return '';
  }

  let result = '';
  const digits: string[] = num.toString().split('').reverse();

  for (const [i, digit_] of digits.entries()) {
    const digit = Number.parseInt(digit_, 10);
    const unitIndex = i % 5;

    // 修复点：确保 digit 是有效索引（0-9）
    if (
      !isNaN(digit) &&
      chineseDigits[digit] !== undefined &&
      chineseUnits[unitIndex] !== undefined
    ) {
      result = chineseDigits[digit] + chineseUnits[unitIndex] + result;
    }
  }

  return result;
}

/**
 * 返回names
 * @param tree - 树形数据
 * @param ids - ids 数组
 * @param fieldName - 需要查找的字段名称
 * @returns names
 */
export function getNamesFromTreeByIds(tree: any, ids: any, fieldName = 'name') {
  function flatten(nodes: any) {
    return nodes.flatMap((node: any) => [
      node,
      ...(node.children ? flatten(node.children) : []),
    ]);
  }

  return flatten(tree)
    .filter((node: any) => ids.includes(node.id))
    .map((node: any) => {
      if (node.isActive === undefined) {
        return node[fieldName];
      } else {
        return node.isActive ? node[fieldName] : `${node[fieldName]}（废弃）`;
      }
    })
    .join('，');
}

export function getFileType(
  file: any,
):
  | 'archive'
  | 'audio'
  | 'excel'
  | 'image'
  | 'other'
  | 'pdf'
  | 'ppt'
  | 'video'
  | 'word' {
  if (!file || typeof file.name !== 'string') return 'other';

  const ext = file.name.trim().split('.').pop()?.toLowerCase() ?? '';

  const IMAGE_EXTS = new Set([
    'bmp',
    'gif',
    'jpeg',
    'jpg',
    'png',
    'svg',
    'webp',
  ]);
  const WORD_EXTS = new Set(['doc', 'docx']);
  const EXCEL_EXTS = new Set(['xls', 'xlsx']);
  const PDF_EXTS = new Set(['pdf']);
  const PPT_EXTS = new Set(['ppt', 'pptx']);
  const VIDEO_EXTS = new Set(['avi', 'flv', 'mov', 'mp4', 'wmv']);
  const AUDIO_EXTS = new Set(['mp3', 'ogg', 'wav']);
  const ARCHIVE_EXTS = new Set(['7z', 'rar', 'zip']);

  if (IMAGE_EXTS.has(ext)) return 'image';
  if (WORD_EXTS.has(ext)) return 'word';
  if (EXCEL_EXTS.has(ext)) return 'excel';
  if (PDF_EXTS.has(ext)) return 'pdf';
  if (PPT_EXTS.has(ext)) return 'ppt';
  if (VIDEO_EXTS.has(ext)) return 'video';
  if (AUDIO_EXTS.has(ext)) return 'audio';
  if (ARCHIVE_EXTS.has(ext)) return 'archive';

  return 'other';
}

export function formatFileSize(size: number): string {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} KB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  }
}

// 解决科学计数问题导致判断位数不正确
export function getDecimalLength(val: number | string): number {
  if (typeof val !== 'string') {
    val = val.toString();
  }

  // 如果是科学记数法形式，如 1e-9
  if (val.includes('e') || val.includes('E')) {
    const fixed = Number(val).toFixed(20); // 取足够多的精度
    const decimal = fixed.split('.')[1] || '';
    // 去除末尾多余的0
    return decimal.replace(/0+$/, '').length;
  }

  // 正常数字
  const decimal = val.split('.')[1] || '';
  return decimal.length;
}
