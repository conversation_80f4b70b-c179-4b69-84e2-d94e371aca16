<template>
  <ColPage v-bind="colPageProps" content-class="content">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary" size="small">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 左侧表格区域 -->
      <div
        :style="{ minWidth: '200px' }"
        v-else
        class="bg-card flex h-full flex-col rounded-lg border p-2"
      >
        <div class="flex items-center justify-between">
          <ElButton
            type="primary"
            size="small"
            @click="handleMaintenanceVersion"
          >
            维护版本
          </ElButton>
          <ElSelect
            v-model="currentSelectVersion"
            size="small"
            placeholder="请选择机械字典版本"
            style="width: 230px"
            @change="onSelectVersionChange"
            :fit-input-width="true"
          >
            <ElOption
              v-for="item in versions"
              :key="item.id"
              :value="item.id"
              :label="`${item.name}（${EnableStatusText[item.status as keyof typeof EnableStatusText]}）`"
              :title="item.name"
            />
          </ElSelect>
        </div>
        <div class="my-2 flex items-center justify-between">
          <div>
            <ElButton
              :disabled="!actionPermissions.apCreate || isDisableAddClassify"
              type="primary"
              size="small"
              @click="addNewClassify"
            >
              新增分类
            </ElButton>
            <ElButton
              :disabled="
                !actionPermissions.apCreate || isDisableaAdNewLowerLevelClassify
              "
              type="primary"
              size="small"
              @click="addNewLowerLevelClassify"
            >
              新增下级分类
            </ElButton>
          </div>
          <!-- <ElDropdown size="small" @command="handleDropDownItem">
            <ElButton plain type="primary" size="small">
              导入文件
              <IconifyIcon icon="bi:arrow-down" />
            </ElButton>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem command="download"> 模版下载 </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown> -->
        </div>
        <div class="flex-1 overflow-hidden">
          <VxeGrid
            ref="leftGridRef"
            v-bind="leftGridOptions"
            v-on="leftGridMethod"
          >
            <template #empty>
              <span>
                {{
                  currentSelectVersion
                    ? '没有更多数据了！'
                    : '请选择机械字典版本！'
                }}
              </span>
            </template>
          </VxeGrid>
        </div>
      </div>
    </template>
    <div class="bg-card ml-2 flex h-full flex-col rounded-lg border p-2">
      <div class="mb-2 flex items-center">
        <ElButton
          :disabled="!actionPermissions.apCreate || isDisabledAddDetailsBtn"
          type="primary"
          size="small"
          @click="addNewDetails"
        >
          新增明细
        </ElButton>
        <ElInput
          v-model="searchStr"
          class="ml-10"
          size="small"
          placeholder="请输入名称"
          style="width: 250px"
          clearable
          @clear="clearSearch"
          @input="searchDetail"
        >
          <template #prefix>
            <ElIcon class="el-input__icon">
              <Search />
            </ElIcon>
          </template>
        </ElInput>
      </div>
      <div class="flex-1 overflow-hidden">
        <VxeGrid
          ref="rightGridRef"
          v-bind="rightGridOptions"
          v-on="rigtGridMethod"
        >
          <!--  业务成本科目名称 -->
          <template #businessCost="{ row }">
            <ElTreeSelect
              class="treeSelect-box"
              multiple
              collapse-tags
              :data="businessCostAccountTreeList"
              v-model="row.businessCostSubjectDetailsIds"
              show-checkbox
              check-strictly
              default-expand-all
              node-key="id"
              :props="{
                disabled: (data: any) => !data.isLeaf,
                label: (data: any) =>
                  data.isActive ? data.name : `${data.name}（废弃）`,
                children: 'children',
              }"
              filterable
              :teleported="false"
              :filter-node-method="filterNodeMethod"
            />
          </template>
          <template #businessCost_default="{ row }">
            <span>
              {{
                filterBusinessCostAccountLabel(
                  row.businessCostSubjectDetailsIds,
                )
              }}
            </span>
          </template>
        </VxeGrid>
      </div>
    </div>
    <!-- 新增版本 -->
    <AddVersion
      v-if="versionDialog"
      v-model:visible="versionDialog"
      @refresh="refresh"
    />
  </ColPage>
</template>

<script setup lang="ts">
import type { VxeGridInstance } from 'vxe-table';

import { computed, nextTick, onMounted, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { Search } from '@element-plus/icons-vue';
import {
  ElButton,
  ElIcon,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
  ElTreeSelect,
} from 'element-plus';
import _ from 'lodash';

import { TreeBusinessCostCategoryDetails } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import {
  AddMachineryDicCategory,
  AddMachineryDicDetail,
  DeleteMachineryDicCategory,
  DeleteMachineryDicDetail,
  ListMachineryDicCategory,
  ListMachineryDicDetail,
  ListMachineryDicVersion,
  MoveMachineryDicCategory,
  MoveMachineryDicDetail,
  UpdateMachineryDicCategory,
  UpdateMachineryDicDetail,
} from '#/api/enterpriseCenter/enterpriseStandards/machineryDictRelease';
import { downloadLocalFile } from '#/utils';
import { getNamesFromTreeByIds } from '#/utils/common';
import { getCurrentPremission } from '#/utils/permission';

import AddVersion from './components/AddVersion.vue';
import { colPageProps, EnableStatus, EnableStatusText } from './data';

const { actionPermissions } = getCurrentPremission();

// 版本数据
const currentSelectVersion = ref('');
const versions = ref();
const versionDialog = ref(false); // 维护版本
function handleMaintenanceVersion() {
  versionDialog.value = true;
}
async function getVersionList() {
  // 获取版本数据
  versions.value = await ListMachineryDicVersion();
}
const curVersionInfo = ref(); // 版本切换
const staticData = {
  // 内置数据
  id: '100',
  name: '全部',
  code: '',
  versions: 'All',
  parentId: null,
  remark: '',
  isActive: true,
  type: 'All',
};

function handleDropDownItem(command: string) {
  if (command === 'download') {
    // TODO 模版下载
    downloadLocalFile('/file/机械字典发布.xlsx', '机械字典发布.xlsx');
  }
}
// 左侧表格
const leftGridRef = ref();
const leftCurrent = ref(); // 左侧选中数据
const businessCostAccountTreeList = ref([]); // 业务成本科目树
const leftGridOptions = reactive<any>({
  // 配置数据
  size: 'mini',
  height: '100%',
  autoresize: true,
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  mouseConfig: {
    selected: true,
  },
  columnConfig: {
    resizable: true,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowDragConfig: {
    isCrossDrag: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: any) {
      // 编辑权限（忽略临时数据）
      if (!actionPermissions.apUpdate && !row._isTempData) {
        return false;
      }
      // 版本数据已启用无法进行编辑
      if (
        curVersionInfo.value &&
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        return false;
      }
      if (row.id === '100') {
        return false;
      }
      // 弃用的数据无法编辑
      if (!row.isActive) {
        return false;
      }
      return true;
    },
  },
  rowClassName: ({ row }: any) => {
    // 通过点击明细反向定位分类的样式
    if (
      row.id &&
      row.isLeaf &&
      row.id === rightCurrent.value?.machineryDictionaryCategoryId &&
      row.id !== leftCurrent.value.id
    ) {
      return 'category-highlight-by-detail';
    }
    // 废弃数据的样式
    if (!row.isActive) {
      return 'discard';
    }

    return null;
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
    code: [{ required: true, message: '编码不得为空！' }],
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入编码',
        },
      },
    },
    {
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
      formatter: ({ cellValue, row }: any) =>
        `${cellValue}${row.isActive ? '' : '（废弃）'}`,
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
    },
  ],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DISCARD',
            name: '废弃',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-warning-triangle' },
          },
          {
            code: 'ENABLED',
            name: '启用',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-success-circle' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      // 1. 内置节点状态控制
      if (
        row.id === '100' ||
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      // 废弃&启用数据控制
      const isActive = row.isActive;
      options[0].forEach((item: any) => {
        switch (item.code) {
          // 删除按钮控制
          case 'DELETE_ROW': {
            item.disabled = !actionPermissions.apDelete && !row._isTempData;
            break;
          }
          case 'DISCARD': {
            item.disabled = !actionPermissions.apUpdate || !isActive;
            break;
          }
          case 'ENABLED': {
            item.disabled = !actionPermissions.apUpdate || isActive;
            break;
          }
          case 'MOVE_DOWN': {
            const targetParentId = row.parentId;
            const tableData = leftGridOptions.data.filter(
              (v: any) => v.parentId === targetParentId && v.isActive !== false,
            );
            const targetIdx = tableData.findIndex((v: any) => v.id === row.id);

            item.disabled =
              !actionPermissions.apUpdate ||
              !isActive ||
              targetIdx === tableData.length - 1;
            break;
          }
          case 'MOVE_UP': {
            const targetParentId = row.parentId;
            const tableData = leftGridOptions.data.filter(
              (v: any) => v.parentId === targetParentId && v.isActive !== false,
            );
            const targetIdx = tableData.findIndex((v: any) => v.id === row.id);
            item.disabled =
              !actionPermissions.apUpdate ||
              !isActive ||
              targetIdx === (targetParentId ? 0 : 1);
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  data: [],
});
const leftGridMethod = {
  async currentRowChange({ row }: any) {
    if (!row) return;
    leftCurrent.value = row;
    rightCurrent.value = null;
    rightGridOptions.data = [];
    searchStr.value = '';
    // 获取明细数据
    if (row.id) {
      await getDetailsList(currentSelectVersion.value, row.id);
    }
    await nextTick(async () => {
      await leftGridRef.value?.setCurrentRow(row);
    });
  },
  cellMenu({ row }: any) {
    const $grid = leftGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
      leftCurrent.value = row;
    }
  },
  async menuClick({ menu }: any) {
    const currentRow = leftCurrent.value;
    switch (menu.code) {
      case 'DELETE_ROW': {
        if (currentRow.id) {
          ElMessageBox.confirm('确定删除该数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            // 存在下级分类无法删除
            const isExist = leftGridOptions.data.find(
              (item: any) => item.parentId === currentRow.id,
            );
            if (isExist)
              return ElMessage.warning('当前数据存在下级分类，无法删除！');
            const res = await DeleteMachineryDicCategory(currentRow.id);
            if (res) {
              leftCurrent.value = null;
              await getClassifyList(currentSelectVersion.value);
              ElMessage.success('操作成功！');
            }
          });
        } else {
          leftCurrent.value = null;
          await getClassifyList(currentSelectVersion.value);
        }

        break;
      }
      case 'DISCARD': {
        // 废弃
        ElMessageBox.confirm('是否废弃该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateMachineryDicCategory(currentRow.id, {
            isActive: false,
            name: leftCurrent.value.name,
            code: leftCurrent.value.code,
            parentId: leftCurrent.value.parentId,
            machineryDictionaryVersionId: leftCurrent.value.versionId,
            // remark: leftCurrent.value.remark,
            // type: leftCurrent.value.type,
          });
          if (res) ElMessage.success('操作成功！');
          await getClassifyList(currentSelectVersion.value);
        });
        break;
      }
      case 'ENABLED': {
        // 启用
        ElMessageBox.confirm('确定启用该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateMachineryDicCategory(currentRow.id, {
            isActive: true,
            name: leftCurrent.value.name,
            code: leftCurrent.value.code,
            parentId: leftCurrent.value.parentId,
            machineryDictionaryVersionId: leftCurrent.value.versionId,
            // type: leftCurrent.value.type,
            // remark: leftCurrent.value.remark,
          });
          if (res) ElMessage.success('操作成功！');
          await getClassifyList(currentSelectVersion.value);
        });
        break;
      }
      case 'MOVE_DOWN': {
        await leftGridRef.value.moveRowTo(leftCurrent.value, 1);
        await MoveMachineryDicCategory(leftCurrent.value.id, 'down');
        await getClassifyList(currentSelectVersion.value);
        break;
      }
      case 'MOVE_UP': {
        await leftGridRef.value.moveRowTo(leftCurrent.value, -1);
        await MoveMachineryDicCategory(leftCurrent.value.id, 'up');
        await getClassifyList(currentSelectVersion.value);
        break;
      }
    }
  },
  async editClosed({ row }: any) {
    if (leftGridRef.value) {
      const errMsg = await leftGridRef.value.validate(row);
      const isUpdateRow = leftGridRef.value.isUpdateByRow(row);
      if (!errMsg) {
        let res = null;
        if (row.id) {
          // 数据是否发生变化
          if (isUpdateRow) {
            // 编辑
            res = await UpdateMachineryDicCategory(row.id, {
              name: row.name,
              code: row.code,
              remark: row.remark,
              parentId: row.parentId,
              type: row.type,
              machineryDictionaryVersionId: row.versionId,
            });
          }
        } else {
          // 新增
          res = await AddMachineryDicCategory({
            name: row.name,
            code: row.code,
            remark: row.remark,
            parentId: row.parentId,
            type: row.type,
            machineryDictionaryVersionId: row.versionId,
          });
          if (res) {
            leftCurrent.value = res;
          }
        }
        if (res) {
          ElMessage.success('操作成功！');
        }
        await getClassifyList(currentSelectVersion.value);
      }
    }
  },
};

async function onSelectVersionChange(versionId: string) {
  leftCurrent.value = null;
  await getClassifyList(versionId);
}

async function getClassifyList(val: string) {
  // 获取分类数据
  curVersionInfo.value = versions.value.find((item: any) => item.id === val);
  const arr = await ListMachineryDicCategory(val);
  leftGridOptions.data = arr || [];
  leftGridOptions.data.unshift(staticData);

  // 切换版本获取业务成本科目树
  await getBusinessCostAccount(
    curVersionInfo.value.businessCostSubjectVersionId,
  );

  // 设置默认选中行、展开树
  const $grid: VxeGridInstance = leftGridRef.value;
  if (!$grid) return;

  const activeRow = leftCurrent.value
    ? leftGridOptions.data.find((item: any) => item.id === leftCurrent.value.id)
    : leftGridOptions.data[0];
  await $grid.setCurrentRow(activeRow);
  await $grid.setAllTreeExpand(true);
  await $grid.scrollToRow(activeRow);
  await leftGridMethod.currentRowChange({ row: activeRow });
}
async function addNewClassify() {
  // 新增分类
  if (!currentSelectVersion.value) return ElMessage.warning('请选择业务版本！');
  const $grid: VxeGridInstance = leftGridRef.value;

  const newClassify = {
    _isTempData: true,
    name: '',
    code: '',
    remark: '',
    parentId: leftCurrent.value.parentId,
    versionId: currentSelectVersion.value,
    isActive: true,
  };
  const { row: newRow } = await $grid.insertAt(newClassify, -1);
  await $grid.setCurrentRow(newRow);
  await $grid.scrollToRow(newRow);
  await $grid.setEditCell(newRow, 'code');
  await leftGridMethod.currentRowChange({ row: newRow });
}
async function addNewLowerLevelClassify() {
  // 新增下级分类
  if (!currentSelectVersion.value) return ElMessage.warning('请选择业务版本！');
  // 点击第全部行 return
  if (leftCurrent.value.id === '100')
    return ElMessage.warning('当前节点无法新增下级分类！');
  if (
    rightGridOptions.data.some(
      (item: any) =>
        item.machineryDictionaryCategoryId === leftCurrent.value.id,
    )
  )
    return ElMessage.warning('当前节点下存在明细数据，无法新增下级分类！');

  const $grid: VxeGridInstance = leftGridRef.value;

  const newLowerLevelClassify = {
    _isTempData: true,
    name: '',
    code: '',
    remark: '',
    parentId: leftCurrent.value.id,
    versionId: currentSelectVersion.value,
    isActive: true,
  };
  const { row: newRow } = await $grid.insertChildAt(
    newLowerLevelClassify,
    leftCurrent.value,
    -1,
  );
  await $grid.setCurrentRow(newRow);
  await $grid.scrollToRow(newRow);
  await $grid.setEditCell(newRow, 'code');
  await leftGridMethod.currentRowChange({ row: newRow });
}
const isDisableAddClassify = computed(() => {
  // 新增分类与新增下级分类按钮禁用状态
  // 1.版本已启用按钮禁用
  // 2.未选择分类数据禁用
  // 3.选中的分类数据id为空禁用 - id为空意味着是新增数据
  // 4.分类已被弃用禁用
  return currentSelectVersion.value &&
    curVersionInfo.value.status !== EnableStatus.ENABLED
    ? _.isEmpty(leftCurrent.value) ||
        !leftCurrent.value.id ||
        leftCurrent.value.isActive !== true
    : true;
});

const isDisableaAdNewLowerLevelClassify = computed(() => {
  // 新增分类与新增下级分类按钮禁用状态
  // 1.版本已启用按钮禁用
  // 2.未选择分类数据禁用
  // 3.选中的分类数据id为空禁用 - id为空意味着是新增数据
  // 4.选中的分类数据id为100禁用 - id为100意味着是全部节点
  // 5.分类已被弃用禁用
  return currentSelectVersion.value &&
    curVersionInfo.value.status !== EnableStatus.ENABLED
    ? _.isEmpty(leftCurrent.value) ||
        !leftCurrent.value.id ||
        leftCurrent.value.id === '100' ||
        leftCurrent.value.isActive !== true
    : true;
});

// 右侧表格
const rightGridRef = ref();
const rightCurrent = ref(); // 右侧选中数据
const rightGridOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  autoresize: true,
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  mouseConfig: {
    selected: true,
  },
  rowDragConfig: {
    isCrossDrag: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: false,
    beforeEditMethod({ row }: any) {
      // 编辑权限（忽略临时数据）
      if (!actionPermissions.apUpdate && !row._isTempData) {
        return false;
      }
      // 版本数据已启用无法进行编辑
      if (
        curVersionInfo.value &&
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        return false;
      }
      // 废弃数据不可编辑
      if (!row.isActive) {
        return false;
      }
      return true;
    },
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  rowClassName: ({ row }: any) => {
    // 废弃数据样式
    if (!row.isActive) {
      return 'discard';
    }
    return null;
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DISCARD',
            name: '废弃',
            prefixConfig: { icon: 'vxe-icon-warning-triangle' },
          },
          {
            code: 'ENABLED',
            name: '启用',
            prefixConfig: { icon: 'vxe-icon-success-circle' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      if (curVersionInfo.value.status === EnableStatus.ENABLED) {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      const dataLength = rightGridOptions.data.length;
      const isActive = row?.isActive;

      // 特殊处理：只有一条数据，两个移动按钮都禁用
      const isOnlyOne = dataLength === 1;

      const isFirst = rowIndex === 0;
      const isLast = rowIndex === dataLength - 1;

      options[0].forEach((item: any) => {
        switch (item.code) {
          // 删除按钮控制
          case 'DELETE_ROW': {
            item.disabled = !actionPermissions.apDelete && !row._isTempData;
            break;
          }
          case 'DISCARD': {
            item.disabled = !actionPermissions.apUpdate || !isActive;
            break;
          }
          case 'ENABLED': {
            item.disabled = !actionPermissions.apUpdate || isActive;
            break;
          }
          case 'MOVE_DOWN': {
            item.disabled =
              !actionPermissions.apUpdate || !isActive || isOnlyOne || isLast;
            break;
          }
          case 'MOVE_UP': {
            item.disabled =
              !actionPermissions.apUpdate || !isActive || isOnlyOne || isFirst;
            break;
          }
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
    code: [{ required: true, message: '编码不得为空！' }],
    type: [{ required: true, message: '规格型号不得为空！' }],
  },
  columns: [
    { type: 'seq', width: 50, title: '序号' },
    {
      title: '编码',
      field: 'code',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入编码',
        },
      },
    },
    {
      width: 150,
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
      },
      formatter: ({ cellValue, row }: any) =>
        `${cellValue}${row.isActive ? '' : '（废弃）'}`,
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'specificationModel',
      title: '规格型号',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入规格型号',
        },
      },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'businessCostSubjectDetailsIds',
      title: '业务成本科目名称',
      editRender: {},
      slots: {
        edit: 'businessCost',
        default: 'businessCost_default',
      },
    },
    {
      field: 'accountExplanation',
      title: '核算说明',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入核算说明',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
  ],
  data: [],
});
const rigtGridMethod = {
  // 右侧方法
  async currentRowChange({ row }: any) {
    if (!row) return;
    rightCurrent.value = row;
    const classifyCurRow = leftGridOptions.data.find(
      (item: any) => item.id === row.machineryDictionaryCategoryId,
    );
    // 定位分类
    if (leftGridRef.value && leftGridOptions.data) {
      leftGridRef.value.scrollToRow(classifyCurRow);
    }
  },
  cellMenu({ row }: any) {
    const $grid = rightGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
      rightCurrent.value = row;
    }
  },
  async editClosed({ row }: any) {
    if (!rightGridRef.value) return;
    // 如果数据没有通过校验，直接 return
    const errMsg = await rightGridRef.value.validate(row);
    if (errMsg) return;
    if (row.id) {
      if (rightGridRef.value.isUpdateByRow(row)) {
        const res = await UpdateMachineryDicDetail(row.id, {
          machineryDictionaryVersionId: row.machineryDictionaryVersionId, // 版本id
          machineryDictionaryCategoryId: row.machineryDictionaryCategoryId, // 分类id
          code: row.code, // 编码
          name: row.name, // 机械字典名称
          specificationModel: row.specificationModel, // 规格型号
          remark: row.remark, // 备注
          businessCostSubjectDetailsIds: row.businessCostSubjectDetailsIds, // 业务成本科目明细 id
          accountExplanation: row.accountExplanation, // 核算说明
        });
        if (res) ElMessage.success('操作成功！');
      }
    } else {
      const res = await AddMachineryDicDetail({
        machineryDictionaryVersionId: row.machineryDictionaryVersionId, // 版本id
        machineryDictionaryCategoryId: row.machineryDictionaryCategoryId, // 分类id
        code: row.code, // 编码
        name: row.name, // 机械字典名称
        specificationModel: row.specificationModel, // 规格型号
        remark: row.remark, // 备注
        businessCostSubjectDetailsIds: row.businessCostSubjectDetailsIds, // 业务成本科目明细 id
        accountExplanation: row.accountExplanation, // 核算说明
      });
      if (res) ElMessage.success('操作成功！');
    }

    await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
  },
  async menuClick({ menu }: any) {
    const currentRow = rightCurrent.value;
    switch (menu.code) {
      case 'DELETE_ROW': {
        if (currentRow.id) {
          ElMessageBox.confirm('确定删除该数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            const res = await DeleteMachineryDicDetail(rightCurrent.value.id);
            if (res) {
              rightCurrent.value = null;
              ElMessage.success('操作成功！');
            }
            await getDetailsList(
              currentSelectVersion.value,
              leftCurrent.value.id,
            );
          });
        } else {
          // 临时数据删除
          return (rightGridOptions.data = rightGridOptions.data.filter(
            (item: any) => item.id,
          ));
        }
        break;
      }
      case 'DISCARD': {
        // 废弃
        ElMessageBox.confirm('是否废弃该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateMachineryDicDetail(currentRow.id, {
            isActive: false,
            machineryDictionaryVersionId:
              rightCurrent.value.machineryDictionaryVersionId, // 版本id
            machineryDictionaryCategoryId:
              rightCurrent.value.machineryDictionaryCategoryId, // 分类id
            code: rightCurrent.value.code, // 编码
            name: rightCurrent.value.name, // 机械字典名称
            specificationModel: rightCurrent.value.specificationModel, // 规格型号
            remark: rightCurrent.value.remark, // 备注
            businessCostSubjectDetailsIds:
              rightCurrent.value.businessCostSubjectDetailsIds, // 业务成本科目明细 id
            accountExplanation: rightCurrent.value.accountExplanation, // 核算说明
          });
          if (res) ElMessage.success('操作成功！');
          await getDetailsList(
            currentSelectVersion.value,
            leftCurrent.value.id,
          );
          await nextTick(() => {
            if (rightGridRef.value && rightGridOptions.data) {
              const changeRightCurrent = rightGridOptions.data.find(
                (item: any) => item.id === rightCurrent.value.id,
              );
              rightGridRef.value.setCurrentRow(changeRightCurrent);
            }
          });
        });
        break;
      }
      case 'ENABLED': {
        // 启用
        ElMessageBox.confirm('确定启用该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateMachineryDicDetail(currentRow.id, {
            isActive: true,
            machineryDictionaryVersionId:
              rightCurrent.value.machineryDictionaryVersionId, // 版本id
            machineryDictionaryCategoryId:
              rightCurrent.value.machineryDictionaryCategoryId, // 分类id
            code: rightCurrent.value.code, // 编码
            name: rightCurrent.value.name, // 机械字典名称
            specificationModel: rightCurrent.value.specificationModel, // 规格型号
            remark: rightCurrent.value.remark, // 备注
            businessCostSubjectDetailsIds:
              rightCurrent.value.businessCostSubjectDetailsIds, // 业务成本科目明细 id
            accountExplanation: rightCurrent.value.accountExplanation, // 核算说明
          });
          if (res) ElMessage.success('操作成功！');
          await getDetailsList(
            currentSelectVersion.value,
            leftCurrent.value.id,
          );
          await nextTick(() => {
            if (rightGridRef.value && rightGridOptions.data) {
              const changeRightCurrent = rightGridOptions.data.find(
                (item: any) => item.id === rightCurrent.value.id,
              );
              rightGridRef.value.setCurrentRow(changeRightCurrent);
            }
          });
        });
        break;
      }
      case 'MOVE_DOWN': {
        await rightGridRef.value.moveRowTo(rightCurrent.value, 1);
        await MoveMachineryDicDetail(rightCurrent.value.id, 'down');
        await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        await nextTick(() => {
          if (rightGridRef.value && rightGridOptions.data) {
            const changeRightCurrent = rightGridOptions.data.find(
              (item: any) => item.id === rightCurrent.value.id,
            );
            rightGridRef.value.setCurrentRow(changeRightCurrent);
          }
        });
        break;
      }
      case 'MOVE_UP': {
        await rightGridRef.value.moveRowTo(rightCurrent.value, -1);
        await MoveMachineryDicDetail(rightCurrent.value.id, 'up');
        await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        await nextTick(() => {
          if (rightGridRef.value && rightGridOptions.data) {
            const changeRightCurrent = rightGridOptions.data.find(
              (item: any) => item.id === rightCurrent.value.id,
            );
            rightGridRef.value.setCurrentRow(changeRightCurrent);
          }
        });
        break;
      }
    }
  },
};
const searchStr = ref(''); // 搜索数据
async function searchDetail() {
  if (!currentSelectVersion.value) {
    return ElMessage.warning('请选择机械字典版本！');
  }

  _.debounce(async () => {
    // 如果不在“全部”行，先定位到“全部行”，再进行搜索
    if (leftCurrent.value.id !== '100') {
      const $grid: VxeGridInstance = leftGridRef.value;
      const allRow = leftGridOptions.data.find(
        (item: any) => item.id === '100',
      );
      leftCurrent.value = allRow;
      await $grid.setCurrentRow(allRow);
      await $grid.scrollToRow(allRow);
    }

    rightCurrent.value = null;
    rightGridOptions.data = await ListMachineryDicDetail({
      machineryDictionaryVersionId: currentSelectVersion.value,
      machineryDictionaryCategoryId: leftCurrent.value.id,
      name: searchStr.value,
    });
  }, 300)();
}
async function clearSearch() {
  const allRow = leftGridOptions.data.find((item: any) => item.id === '100');
  leftCurrent.value = allRow;
  rightCurrent.value = null;
}
async function addNewDetails() {
  const $grid: VxeGridInstance = rightGridRef.value;

  // 新增明细
  const newDetails = {
    _isTempData: true,
    machineryDictionaryVersionId: currentSelectVersion.value, // 版本id
    machineryDictionaryCategoryId: leftCurrent.value.id, // 分类id
    code: '', // 编码
    name: '', // 机械字典名称
    specificationModel: '', // 规格型号
    remark: '', // 备注
    businessCostSubjectDetailsIds: [], // 业务成本科目明细 id
    accountExplanation: '', // 核算说明
    isActive: true,
  };
  const { row: newRow } = await $grid.insertAt(newDetails, -1);
  await $grid.setCurrentRow(newRow);
  await $grid.scrollToRow(newRow);
  await $grid.setEditCell(newRow, 'code');
}

async function getDetailsList(versionId: string, categoryId: string) {
  // 获取明细数据
  rightGridOptions.data = await ListMachineryDicDetail({
    machineryDictionaryVersionId: versionId,
    machineryDictionaryCategoryId: categoryId,
  });
}
const isDisabledAddDetailsBtn = computed(() => {
  // 新增明细按钮禁用状态
  // 版本启用禁用
  if (
    currentSelectVersion.value &&
    curVersionInfo.value.status === EnableStatus.ENABLED
  ) {
    return true;
  }
  if (!leftCurrent.value || _.isEmpty(leftCurrent.value)) {
    return true;
  }

  // 如果分类废弃，无法新增明细
  if (!leftCurrent.value.isActive) {
    return true;
  }
  // 是否叶子节点，叶子节点才可以新增明细
  if (!leftCurrent.value.isLeaf) {
    return true;
  }

  return leftCurrent.value.id ? leftCurrent.value.id === '100' : true;
});

// 获取业务成本科目树
async function getBusinessCostAccount(businessVersionId: string) {
  businessCostAccountTreeList.value = businessVersionId
    ? await TreeBusinessCostCategoryDetails(businessVersionId)
    : [];
}
// 业务成本科目树选中
function filterBusinessCostAccountLabel(ids: [string]) {
  return getNamesFromTreeByIds(businessCostAccountTreeList.value, ids);
}

const filterNodeMethod = (value: string, data: any) =>
  data.name.includes(value);

// 刷新接口数据
async function refresh() {
  await getVersionList();

  const currentVersion = versions.value.find(
    (x: any) => x.id === currentSelectVersion.value,
  );
  if (currentVersion) {
    currentSelectVersion.value = currentVersion.id;
  } else {
    const activeItem = versions.value.find(
      (v: any) => v.status === EnableStatus.ENABLED,
    );
    currentSelectVersion.value = activeItem
      ? activeItem.id
      : versions.value[0]?.id;
  }

  if (currentSelectVersion.value) {
    await getClassifyList(currentSelectVersion.value);
  }
}

// 初始化
async function init() {
  await getVersionList();
  let activeItem = versions.value.find(
    (v: any) => v.status === EnableStatus.ENABLED,
  );
  // 如果没有启用的版本，默认选中第一个版本
  if (!activeItem) {
    activeItem = versions.value[0];
  }
  if (activeItem) {
    currentSelectVersion.value = activeItem.id;
    await getClassifyList(currentSelectVersion.value);
  }
}

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
::v-deep(.vxe-table .vxe-body--row.category-highlight-by-detail) {
  background-color: #fffacd;
}

::v-deep(.vxe-table .vxe-body--row.discard) {
  color: #cdcdcd;
}

.content {
  height: 100%;
}

.showBox {
  position: absolute;
  bottom: -40px;
  left: 20px;
  width: 95.5%;

  :deep(.el-card__header) {
    padding: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    padding: 6px;
    font-size: 14px;

    .icon {
      font-size: 15px;
      cursor: pointer;
    }
  }
}

.drawer-box {
  position: absolute;
  bottom: 10px;
  left: 20px;
  width: 95.5%;

  :deep(.el-card__body) {
    padding: 10px;
  }

  :deep(.el-card__header) {
    padding: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    padding: 6px;
    font-size: 14px;

    .icon {
      font-size: 15px;
      cursor: pointer;
    }
  }
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(100%);
}

.slide-up-enter-active {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.slide-up-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.selectPopup {
  z-index: 999999 !important;
  background-color: #f06 !important;
}

:deep(.treeSelect-box) {
  .el-select__wrapper {
    height: 33px !important;

    .el-select__selection {
      height: 25px !important;
    }
  }
}
</style>
