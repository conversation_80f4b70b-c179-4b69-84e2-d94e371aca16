<template>
  <ColPage v-bind="colPageProps">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 左侧表格区域 -->
      <div
        :style="{ minWidth: '200px', overflow: 'hidden' }"
        v-else
        class="bg-card h-full rounded-lg border p-2 pb-4"
      >
        <div class="mb-2 flex items-center">
          <ElButton
            v-auth="actionPermissions.apCreate"
            type="primary"
            size="default"
            @click="handleAdd"
          >
            新增
          </ElButton>
        </div>
        <VxeGrid
          ref="leftGridRef"
          v-bind="leftGridOptions"
          v-on="leftMethodOptions"
        >
          <template #status="{ row }">
            <!-- <div>{{ row.status }}</div> -->
            <ElTag
              :type="
                row.status === VersionStatus.PUBLISHED ? 'success' : 'primary'
              "
            >
              {{ getVersionStatusLabel(row.status) }}
            </ElTag>
          </template>
        </VxeGrid>
      </div>
    </template>
    <div class="bg-card ml-2 h-full rounded-lg border p-2 pb-5">
      <div class="mb-2 flex items-center">
        <ElButton
          type="primary"
          class="w-[100px]"
          size="default"
          :disabled="
            !(actionPermissions.apCreate || actionPermissions.apUpdate) ||
            disabledSave
          "
          @click="handleSave"
        >
          保存
        </ElButton>
        <ElButton
          type="primary"
          class="w-[100px]"
          size="default"
          :disabled="!actionPermissions.apUpdate || disabledPublish"
          @click="handlePublish"
        >
          {{
            orgInfoForm.status === VersionStatus.PUBLISHED ? '取消发布' : '发布'
          }}
        </ElButton>
      </div>
      <div v-if="orgInfoForm.id">
        <div class="p-10">
          <ElForm
            ref="formEl"
            :model="orgInfoForm"
            :rules="rules"
            :disabled="
              !(actionPermissions.apCreate || actionPermissions.apUpdate) ||
              orgInfoForm.status === VersionStatus.PUBLISHED
            "
            lass="demo-form-inline"
          >
            <ElDescriptions :column="3" border size="default">
              <ElDescriptionsItem label="公司名称">
                <template #label>
                  <div class="cell-item flex">
                    <div class="mt-[-4px] text-red-600">*</div>
                    <div>公司名称</div>
                  </div>
                </template>

                <ElFormItem prop="companyName" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.companyName"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="统一社会信用代码">
                <template #label>
                  <div class="cell-item flex">
                    <div class="mt-[-4px] text-red-600">*</div>
                    <div>统一社会信用代码</div>
                  </div>
                </template>
                <ElFormItem prop="unifiedSocialCreditCode" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.unifiedSocialCreditCode"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="注册地址">
                <ElFormItem prop="registeredAddress" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.registeredAddress"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="企业所在地">
                <ElFormItem prop="companyLocation" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.companyLocation"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="纳税人身份">
                <ElFormItem prop="taxpayerType" label="">
                  <ElSelect v-model="orgInfoForm.taxpayerType" size="large">
                    <ElOption label="一般纳税人" value="一般纳税人" />
                    <ElOption label="小规模纳税人" value="小规模纳税人" />
                  </ElSelect>
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="业务电话">
                <ElFormItem prop="businessPhone" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.businessPhone"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="开户银行">
                <ElFormItem prop="bankName" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.bankName"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="开户地址">
                <ElFormItem prop="bankAddress" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.bankAddress"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="开户账号">
                <ElFormItem prop="bankAccount" label="">
                  <ElInput
                    class="h-[38px]"
                    v-model="orgInfoForm.bankAccount"
                    placeholder=""
                  />
                </ElFormItem>
              </ElDescriptionsItem>
            </ElDescriptions>
          </ElForm>
        </div>

        <ExtrasPanel
          :visible-option="['CHANGERECORD']"
          :record-filed-key="filedKeyName"
          :record-list="changeLogRecord"
        />
      </div>
      <div v-else-if="leftGridOptions.data.length <= 0">
        <div class="flex items-center justify-center p-10">请先新增版本</div>
      </div>
    </div>
  </ColPage>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  addBusinessVersion,
  cancelPublishBusinessInfo,
  delBusinessInfo,
  getBusinessInfoChangeLogList,
  getBusinessInfoList,
  moveBusinessInfo,
  publishBusinessInfo,
  saveBusinessInfo,
  updateBusinessVersion,
} from '#/api/enterpriseCenter/companyBasicInfo/companyBasicInfo';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import {
  getVersionStatusLabel,
  VersionStatus,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import { setCurrentRow } from '#/utils/vxeTool';

import { colPageProps } from './data';

const { actionPermissions } = getCurrentPremission();

const filedKeyName = {
  companyName: '公司名称',
  unifiedSocialCreditCode: '统一社会信用代码',
  registeredAddress: '注册地址',
  companyLocation: '企业所在地',
  taxpayerType: '纳税人身份',
  businessPhone: '业务电话',
  bankName: '开户银行',
  bankAddress: '开户地址',
  bankAccount: '开户账号',
} as any;

const rules = reactive({
  companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  unifiedSocialCreditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    {
      pattern: /^[A-Z0-9]{18}$/i,
      message: '请输入由字母和数字组成的18位字符',
      trigger: 'blur',
    },
  ],
});

let isFinishInsert = true;
const isChoiceOrg = ref(false);
const currentItem = ref();
const leftGridRef = ref();
const shouldLogShow = ref(false);
const formEl = ref();
// 公司基本信息表单
const orgInfoForm = reactive({
  id: '',
  companyName: '',
  unifiedSocialCreditCode: '',
  registeredAddress: '',
  companyLocation: '',
  taxpayerType: '',
  businessPhone: '',
  bankName: '',
  bankAddress: '',
  bankAccount: '',
  status: null,
});
const disabledSave = computed(() => {
  return (
    orgInfoForm.id === '' ||
    !isFinishInsert ||
    !orgInfoForm.status ||
    orgInfoForm.status === VersionStatus.PUBLISHED
  );
});
const disabledPublish = computed(() => {
  return (
    orgInfoForm.id === '' ||
    !isFinishInsert ||
    !orgInfoForm.status ||
    orgInfoForm.status === VersionStatus.DRAFT
  );
});

// 表格数据配置
const leftGridOptions = reactive<any>({
  height: '98%',
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  loading: true,
  // headerRowClassName: 'bg-sky-200 text-black-800',
  // rowClassName: ({ row }: { row: { parentId: string | null } }) => {
  //   if (row.parentId) {
  //     return '';
  //   } else {
  //     return 'bg-gray-100';
  //   }
  // },
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  // treeConfig: {
  //   rowField: 'id',
  //   parentField: 'parentId',
  //   transform: true,
  //   expandAll: true,
  // },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: any) {
      // 编辑权限（忽略临时数据）
      if (!actionPermissions.apUpdate && !row._isTempData) {
        return false;
      }
      return row.status !== VersionStatus.PUBLISHED;
    },
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
  },
  columns: [
    {
      type: 'seq',
      field: 'seq',
      title: ' ',
      width: 80,
    },
    {
      field: 'companyVersion',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
  ],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      const isDisabled = row.status === VersionStatus.PUBLISHED;

      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled =
              (!actionPermissions.apDelete && !row._isTempData) || isDisabled;
            break;
          }
          case 'MOVE_DOWN': {
            item.disabled =
              !actionPermissions.apUpdate ||
              rowIndex === leftGridOptions.data.length - 1;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = !actionPermissions.apUpdate || rowIndex === 0;
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  data: [],
});
const leftMethodOptions = reactive({
  // filterChange(data: any) {},

  // 点击列
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    handleChoice(row);
  },
  // 右键菜单
  cellMenu({ row }: any) {
    currentItem.value = row;
    setCurrentRow(leftGridOptions.data, currentItem.value, leftGridRef.value);
  },

  // 表格确认修改
  async editClosed({ row }: any) {
    insureAddorEdit(row);
  },
  async menuClick({ menu, row }: any) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      if (id) {
        ElMessageBox.confirm('你确定要删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error',
        }).then(async () => {
          delData(id);
        });
      } else {
        if (leftGridRef.value) {
          leftGridRef.value.remove(row);

          isFinishInsert = true;
        }
      }
    }
    if (menu.code === 'MOVE_UP') {
      const moveType = 'up';
      moveData(moveType);
    }
    if (menu.code === 'MOVE_DOWN') {
      const moveType = 'down';
      moveData(moveType);
    }
  },
});

const changeLogData = ref();
const changeLogRecord = ref();
// 改变变更记录状态
function changeLogStatus(type?: string) {
  shouldLogShow.value = type === 'close' ? false : !shouldLogShow.value;
}

// 新增行
function handleAdd() {
  const customerRow = {
    _isTempData: true,
    status: VersionStatus.DRAFT,
    companyVersion: '',
    remark: '',
  };
  nextTick(() => {
    if (isFinishInsert) {
      leftGridRef.value && leftGridRef.value.insertAt(customerRow);
      isFinishInsert = false;
    }
  });
}

// 确认新增数据
async function insureAddorEdit(row: any) {
  if (row.id) {
    const id = row.id;
    const params = {
      companyVersion: row.companyVersion,
      remark: row.remark,
    };
    const res = await updateBusinessVersion(id, params);

    if (res) {
      ElMessage.success('修改成功');
      isFinishInsert = true;
      refreshData();
    }
  } else {
    const params = {
      companyVersion: row.companyVersion,
      remark: row.remark,
    };
    const res = await addBusinessVersion(params);
    if (res) {
      ElMessage.success('添加成功');
      isFinishInsert = true;
      refreshData();
    }
  }
}

// 选择数据后的操作
function handleChoice(row: any) {
  orgInfoForm.id = row.id;
  orgInfoForm.companyName = row.companyName;
  orgInfoForm.unifiedSocialCreditCode = row.unifiedSocialCreditCode;
  orgInfoForm.registeredAddress = row.registeredAddress;
  orgInfoForm.companyLocation = row.companyLocation;
  orgInfoForm.taxpayerType = row.taxpayerType;
  orgInfoForm.businessPhone = row.businessPhone;
  orgInfoForm.bankName = row.bankName;
  orgInfoForm.bankAddress = row.bankAddress;
  orgInfoForm.bankAccount = row.bankAccount;
  orgInfoForm.status = row.status;
  isChoiceOrg.value = true;

  nextTick(() => {
    formEl.value && formEl.value?.clearValidate();
  });
  getChangeLog();
}
// 保存操作
async function handleSave() {
  const isValidate = await formEl.value.validate();
  if (!isValidate) return;
  const id = orgInfoForm.id;
  const params = {
    companyName: orgInfoForm.companyName,
    unifiedSocialCreditCode: orgInfoForm.unifiedSocialCreditCode,
    registeredAddress: orgInfoForm.registeredAddress,
    companyLocation: orgInfoForm.companyLocation,
    taxpayerType: orgInfoForm.taxpayerType,
    businessPhone: orgInfoForm.businessPhone,
    bankName: orgInfoForm.bankName,
    bankAddress: orgInfoForm.bankAddress,
    bankAccount: orgInfoForm.bankAccount,
  };
  await saveBusinessInfo(id, params);
  await refreshData();

  const newData = leftGridOptions.data.find(
    (v: any) => v.id === currentItem.value.id,
  );
  orgInfoForm.status = newData.status;
  // handleChoice(newData);
}
// 发布操作
async function handlePublish() {
  ElMessageBox.confirm(
    `你确定要${orgInfoForm.status === VersionStatus.PUBLISHED ? '取消' : ''}发布吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(async () => {
    const id = orgInfoForm.id;
    if (orgInfoForm.status === VersionStatus.PUBLISHED) {
      const res = await cancelPublishBusinessInfo(id);
      if (res) {
        ElMessage.success('取消发布成功');
      }
    } else {
      const res = await publishBusinessInfo(id);
      if (res) {
        ElMessage.success('发布成功');
      }
    }
    await refreshData();

    const newData = leftGridOptions.data.find(
      (v: any) => v.id === currentItem.value.id,
    );
    orgInfoForm.status = newData.status;
  });
}
// 删除操作
async function delData(id: string) {
  const businessId = id ?? orgInfoForm.id;
  const res = await delBusinessInfo(businessId);
  if (res) {
    ElMessage.success('删除成功');
    orgInfoForm.id = null;
    await refreshData();
  }
}
// 移动操作
type moveType = 'down' | 'up';
async function moveData(type: moveType) {
  const id = orgInfoForm.id;
  const params = { id, moveType: type };
  const res = await moveBusinessInfo(params);
  if (res) {
    refreshData();
  }
}
// 获取变更记录
async function getChangeLog() {
  const id = orgInfoForm.id;
  const records = await getBusinessInfoChangeLogList(id);
  changeLogRecord.value = records.map((item: any) => {
    return {
      id: item.id, // id
      createAt: item.createAt, // 创建时间
      createByName: item.createByName, // 创建人
      versionNumber: item.versionNumber, // 版本号 用于按照其数据进行组合
      fieldKey: item.fieldKey, // 字段key
      newValue: item.newValue, // 新值
      oldValue: item.oldValue, // 旧值
    };
  });
}
// 刷新
async function refreshData() {
  await getList();

  setCurrentRow(leftGridOptions.data, leftGridRef.value, currentItem.value);

  await getChangeLog();
}
// 获取公司版本列表
async function getList() {
  leftGridOptions.loading = true;
  const res = await getBusinessInfoList();
  leftGridOptions.data = res;
  leftGridOptions.loading = false;
}

async function init() {
  leftGridOptions.loading = true;
  const res = await getBusinessInfoList();
  leftGridOptions.data = res;
  leftGridOptions.loading = false;
  // 初始化时选中第一行
  if (res.length > 0) {
    handleChoice(res[0]);
    currentItem.value = res[0];
    setCurrentRow(leftGridOptions.data, leftGridRef.value, currentItem.value);
  }
}

onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 0;
}

.el-input__wrapper {
  border: none !important;
  box-shadow: none !important;
}

.el-input.is-focus .el-input__wrapper {
  border: none !important;
  box-shadow: none !important;
}

.drawer-slide-enter-active,
.drawer-slide-leave-active {
  transition:
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s;
}

.drawer-slide-enter-from,
.drawer-slide-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.drawer-slide-enter-to,
.drawer-slide-leave-from {
  opacity: 1;
  transform: translateX(0);
}
</style>
