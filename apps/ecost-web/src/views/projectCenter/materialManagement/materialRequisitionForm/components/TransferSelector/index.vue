<template>
  <div
    class="flex h-[540px]"
    :class="[
      display === 'col'
        ? 'flex-col justify-between'
        : 'items-center justify-between',
    ]"
  >
    <!-- 左部确认区 -->
    <div :class="display === 'col' ? 'h-full w-full' : 'h-full w-[67%]'">
      <Choices
        :choice-class-data="choiceClass"
        :choice-detail-data="choiceDetail"
        :selection-data="selections"
        @select="choicesSelect"
        @add="addSelection"
      />
    </div>
    <!-- 中部按钮区 -->
    <div
      class="flex items-center justify-center"
      :class="[display === 'col' ? 'mt-[-18px]' : '']"
    >
      <ElIcon size="26" :class="display === 'col' ? 'rotate-90' : ''">
        <DArrowRight />
      </ElIcon>
    </div>
    <!-- 右部确认区 -->
    <div
      :class="display === 'col' ? 'mt-[-20px] h-full w-full' : 'h-full w-[30%]'"
    >
      <Selections
        ref="selectionsRef"
        :selection-data="selections"
        @remove="removeSelection"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';

import { DArrowRight } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';
import { v4 as uuidV4 } from 'uuid';

import Choices from './components/LeftChoices.vue';
import Selections from './components/RightSelections.vue';

type DisplayType = 'col' | 'row';
const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    display?: DisplayType;
    selectionData: any[];
  }>(),
  {
    selectionData: () => [],
    choiceClassData: () => [],
    choiceDetailData: () => [],

    display: 'row',
  },
);

const emit = defineEmits<{
  (e: 'update:selectionData', selectionData: any[]): void;
  (e: 'select', data: any): void;
  (e: 'classSearch', text: any): void;
  (e: 'detailSearch', text: any, row: any): void;
}>();

// 确认区
const selections = ref(props.selectionData);
const selectionsRef = ref();
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
  },
  { deep: true, immediate: true },
);
// 选择区:分类
const choiceClass = ref<any[]>([]);
const currentClassItem = ref();
watch(
  () => props.choiceClassData,
  (nval) => {
    choiceClass.value = nval;
  },
  { deep: true, immediate: true },
);
// 选择区:明细
const choiceDetail = ref<any[]>([]);
watch(
  () => props.choiceDetailData,
  (nval) => {
    choiceDetail.value = nval;
  },
  { deep: true, immediate: true },
);

// 选中分类
async function choicesSelect(row: any) {
  currentClassItem.value = row;
  emit('select', row);
}

// 选择器改变
async function addSelection(row: any) {
  const rowData = structuredClone({ allocationQuantity: '', ...row });

  // 领料数量确认区域表格，换一个row.id，使其唯一
  rowData.materialId = row.id;
  rowData.id = uuidV4();

  const newSelections = [...selections.value, rowData];
  selections.value = newSelections;
  emit('update:selectionData', newSelections);
}

// 已选择的数据改变
async function removeSelection({ row }: any) {
  const idx = selections.value.findIndex((v) => v.id === row.id);
  if (idx !== -1) {
    const newSelections = selections.value.filter((item) => item.id !== row.id);
    selections.value = newSelections;
    emit('update:selectionData', newSelections);
  }
}

defineExpose({
  validateSelections: async () => {
    return await selectionsRef.value?.validateTable();
  },
});
</script>
<style scoped lang="scss"></style>
