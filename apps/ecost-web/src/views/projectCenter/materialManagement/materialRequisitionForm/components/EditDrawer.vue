<template>
  <div class="edit-drawer relative h-[100%]">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="h-full w-full">
        <div class="header box-border w-full pl-4 pr-4">
          <div class="flex h-[60px] w-full items-center justify-between">
            <div class="header-left flex items-center justify-between"></div>
            <div class="header-right flex">
              <div class="btn-group ml-4 pr-4">
                <ElButton
                  type="primary"
                  size="default"
                  @click="insureSubmit"
                  v-auth="actionPermissions.apUpdate"
                >
                  {{
                    localInfoData.submitStatus === SubmitStatus.PENDING
                      ? '提交'
                      : '取消提交'
                  }}
                </ElButton>
                <!-- <ElButton type="default" size="default" disabled>
                导出单据
              </ElButton> -->
              </div>
              <div class="flex">
                <IconifyIcon
                  @click="() => emit('move', -1)"
                  class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-left-line"
                />
                <IconifyIcon
                  @click="() => emit('move', 1)"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-right-line"
                />
              </div>
              <div>
                <ElTooltip
                  :content="isFullScreen ? '收起' : '全屏'"
                  placement="bottom"
                >
                  <IconifyIcon
                    @click="isFullScreen = !isFullScreen"
                    class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                    :icon="
                      isFullScreen
                        ? 'majesticons:arrows-collapse-full'
                        : 'majesticons:arrows-expand-full-line'
                    "
                  />
                </ElTooltip>
              </div>
              <div>
                <IconifyIcon
                  @click="closeClick"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="ep:close"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content h-[calc(100%-60px)] p-[24px] pb-0 pt-2">
          <div class="relative flex h-[68px] items-center justify-center">
            <div class="title flex text-[24px] font-bold">
              领料单 (
              <div
                :class="{
                  'text-red-500':
                    localInfoData.submitStatus === SubmitStatus.PENDING,
                  'text-orange-500':
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.REJECTED,
                  'text-green-500':
                    localInfoData.auditStatus === AuditStatus.APPROVED ||
                    localInfoData.submitStatus === SubmitStatus.SUBMITTED,
                }"
              >
                <!-- 如果是 待审核 ： 提交状态 ， 如果不是 ： 审核状态 -->
                {{
                  localInfoData.auditStatus === AuditStatus.PENDING
                    ? getSubmitStatusLabel(localInfoData.submitStatus)
                    : getAuditStatusLabel(localInfoData.auditStatus)
                }}
              </div>
              )
            </div>
            <div
              class="qrcode absolute right-20 flex items-center"
              v-if="localInfoData.submitStatus === SubmitStatus.SUBMITTED"
            >
              <QrcodeVue value="调拨单" :size="80" />
              <!-- <div class="ml-4 text-sm">导出{{ 2 }}次</div> -->
            </div>
          </div>

          <div class="content flex h-[calc(100%-70px)] w-full flex-col">
            <ElForm
              :model="formData"
              class="top-form grid grid-cols-3 gap-x-20 gap-y-1 pb-1 pt-3"
            >
              <ElFormItem
                label="项目名称："
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formData.orgName }}
              </ElFormItem>
              <ElFormItem />

              <ElFormItem
                label="单据编码："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>

              <ElFormItem
                label="领料单位："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElTooltip placement="top" :content="formData.departmentName">
                  <div class="flex w-full justify-center">
                    <ElSelect
                      size="large"
                      v-model="formData.supplierId"
                      placeholder=""
                      clearable
                      filterable
                      :disabled="!localEditable"
                      @change="handleDepartmentChange"
                      @visible-change="handleSelectVisibleChange"
                    >
                      <template #empty>
                        <span>{{ formData.supplierId }}</span>
                      </template>
                      <ElOption
                        v-for="v in supplierOptions"
                        :key="v.value"
                        :label="v.label"
                        :value="v.value"
                      />
                    </ElSelect>
                  </div>
                </ElTooltip>
              </ElFormItem>

              <ElFormItem
                label="使用部位："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <div class="flex w-full justify-center">
                  <ElInput
                    size="large"
                    v-model="formData.partName"
                    placeholder="请输入部位名称"
                    :disabled="!localEditable"
                    @change="insureSave"
                  />
                </div>
              </ElFormItem>

              <ElFormItem
                label="领料日期："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElDatePicker
                  size="large"
                  v-model="formData.requisitionDate"
                  type="date"
                  placeholder=""
                  @change="insureSave"
                  :clearable="false"
                  :disabled="!localEditable || !modifyAccountTime"
                />
              </ElFormItem>
            </ElForm>

            <div class="flex-1 overflow-auto">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <template #seq="{ row, $rowIndex }">
                  <div v-if="!row.internal">{{ $rowIndex + 1 }}</div>
                </template>
                <template #businessCostSubjectDefault="{ row }">
                  <div>{{ row.businessCostSubjectId }}</div>
                </template>
                <template #businessCostSubjectEdit="{ row }">
                  <VxeSelect
                    v-model="row.businessCostSubjectId"
                    :options="businessCostSubjectSelectOptions"
                    @change="handleBusinessCostSubjectChange(row)"
                    @visible-change="handleBusinessCostSubjectVisibleChange"
                  />
                </template>
                <template #materialName="{ row }">
                  <div
                    v-if="row.internal"
                    class="flex items-center justify-center"
                  >
                    <ElButton
                      size="small"
                      @click="transferDataClick"
                      :disabled="!localEditable"
                    >
                      +
                    </ElButton>
                  </div>
                  <div class="flex items-center" v-else>
                    <ElButton
                      v-if="row.parentId"
                      type="text"
                      @click="jumpTo(row)"
                    >
                      {{ row.materialName }}
                    </ElButton>
                    <div v-else>{{ row.materialName }}</div>
                  </div>
                </template>

                <template #requisitionAmount="{ row }">
                  <div class="font-bold text-orange-500">
                    {{ amountFormat({ cellValue: row.amount }) }}
                  </div>
                </template>
              </VxeGrid>
            </div>

            <ElForm class="info grid grid-cols-3 gap-x-20 pt-2">
              <ElFormItem label="施工员：" size="default" label-position="left">
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem label="发料员：" size="large" label-position="left">
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem label="领料员：" size="large" label-position="left">
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem label="编制人：" size="large" label-position="left">
                {{ formInfo.creator }}
              </ElFormItem>
            </ElForm>
          </div>

          <div class="footer flex items-center">
            <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
          </div>
        </div>

        <ExtrasPanel
          v-model:open-status="extrasOpen"
          v-model:cur-tab="extrasTab"
          :info-data="localInfoData"
          :file-list="fileList"
          :visible-option="['ANNEX']"
          :editable="localEditable"
          list-type="picture"
          @del-annex="removeAnnex"
          @success-annex="addAnnex"
        />
      </div>

      <AddOrEditMaterial
        v-model:visible="addOrEditMaterialVisible"
        :info-data="addMaterialinfoData"
        title="选择材料"
        @refresh="refreshData"
      />
    </ElDrawer>
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, provide, reactive, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { IconifyIcon } from '@vben/icons';

import Big from 'big.js';
import {
  dayjs,
  ElButton,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addRequisitionAttachment,
  deleteRequisitionDetail,
  delRequisitionAttachment,
  editRequisitionBill,
  editRequisitionDetail,
  getBusinessCostSubjectList,
  getRequisitionAttachmentList,
  getRequisitionDepartmentList,
  getRequisitionDetailList,
  moveRequisitionDetail,
} from '#/api/projectCenter/projectCenter/materialRequisitionForm';
import {
  getIsEditParams,
  ParamsNamesType,
} from '#/api/systemManagementApi/orgParams';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import { usePageReturnState } from '#/store';
import {
  AuditStatus,
  getAuditStatusLabel,
  getSubmitStatusLabel,
  SubmitStatus,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import { amountFormat, setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import { TabEnum } from '../type';
import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialEntryCheckEditDrawer',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();
const pageReturnState = usePageReturnState();
const route = useRoute();
const router = useRouter();
const { actionPermissions } = getCurrentPremission();
const extrasOpen = ref(false);
const extrasTab = ref('ANNEX');

// 附件数据
const fileList = ref([]);

// 业务成本科目列表
const businessCostSubjectList: any[] = [];

// 业务成本科目下拉数据
const businessCostSubjectSelectOptions = ref<{ label: string; value: any }[]>(
  [],
);

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);

// 表单数据
interface FormDataType {
  id: string;
  orgName: string;
  departmentName: string;
  supplierId: '';
  partName: string;
  requisitionDate: string;
  code: string;
}

const formData = ref<FormDataType>({
  id: '',
  orgName: '',
  departmentName: '',
  supplierId: '',
  partName: '',
  requisitionDate: '',
  code: '',
});
const oldFormData = ref({
  id: '',
  orgName: '',
  departmentName: '',
  supplierId: '',
  partName: '',
  requisitionDate: '',
  code: '',
});
// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval: any) => {
    localInfoData.value = nval;

    // 调用获取材料清单列表
    await getList();
    // 调用获取附件列表
    await getAnnexlist();

    const { year, day, month, creator } = nval;
    const form = {
      id: nval.id,
      orgName: nval.orgName,
      departmentName: nval.departmentName,
      supplierId: nval.supplierId,
      partName: nval.partName,
      requisitionDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
      code: nval.code,
    };
    formData.value = form;
    oldFormData.value = structuredClone(form);
    formInfo.value = {
      user: '',
      creator,
    };
  },
);

const localEditable = computed(() => {
  return (
    localInfoData.value.submitStatus === SubmitStatus.PENDING &&
    actionPermissions.apUpdate
  );
});

watch(
  () => formData,
  (nval, oval: any) => {
    const { year, day, month } = oval;
    oldFormData.value = {
      id: oval.id,
      orgName: oval.orgName,
      supplierId: oval.supplierId,
      departmentName: oval.departmentName,
      partName: oval.partName,
      requisitionDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
      code: oval.code,
    };
  },
);

// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '64%';
});

const addOrEditMaterialVisible = ref(false);

const addMaterialinfoData = ref();

// 内置节点数据
const addBtnBlockItem = {
  id: '',
  parentId: '',
  name: null,
  internal: true, // 代表是内置节点
};

const tableRef = ref();
const currentItem = ref();
const unitOptions = ref([]);

const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: ' ',
    width: '50',
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '160',
    slots: {
      default: 'materialName',
    },
    treeNode: true,
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
    formatter: ({ cellValue, row }: any) => {
      if (row.internal) return '';

      return row.parentId
        ? dayjs(cellValue).format('YYYY/MM/DD')
        : `${cellValue}`;
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '100',
  },
  {
    field: 'inventoryQuantity',
    title: '库存数量',
    minWidth: '100',
  },
  {
    field: 'actualQuantity',
    title: '实发数量',
    minWidth: '100',
    editRender: {
      name: 'VxeNumberInput',
      props: {
        placeholder: '请输入实发数量',
      },
    },
  },
  {
    field: 'price',
    title: '领料单价',
    minWidth: '100',
    formatter: amountFormat,
  },
  {
    field: 'amount',
    title: '领料金额（元）',
    minWidth: '100',
    slots: {
      default: 'requisitionAmount',
      footer: 'requisitionAmount',
    },
  },
  {
    field: 'businessCostSubjectId',
    title: '成本科目挂接',
    minWidth: '120',
    formmater: {},
    slots: {
      default: 'businessCostSubjectDefault',
      edit: 'businessCostSubjectEdit',
    },
    // editRender: {
    //   name: 'VxeSelect',
    //   options: businessCostSubjectSelectOptions,
    //   props: {
    //     size: 'mini',
    //     placeholder: '请选择合同类型',
    //   },
    // },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  cellClassName: ({ row, column }: any) => {
    return !column.editRender ||
      (column.field === 'actualQuantity' && row.parentId) ||
      column.editRender.enabled === false ||
      row.internal
      ? 'bg-gray-100'
      : '';
  },
  editRules: {
    actualQuantity: [
      {
        required: true,
        // message: '请输入小数位<=8位的调拨数量',
        // pattern: /^(?!0+(?:\.0+)?$)\d+(?:\.\d{1,8})?$/,
        validator: ({ row }: any) => {
          if (row.actualQuantity <= 0) {
            return new Error('实发数量必须大于0');
          }
          const pattern = /^\d+(?:\.\d{1,8})?$/;
          if (!pattern.test(row.actualQuantity)) {
            return new Error('请输入小数位<=8位的实发数量');
          }

          if (row.actualQuantity > row.inventoryQuantity) {
            return new Error('实发数量不能大于库存数量');
          }
        },
      },
    ],
    businessCostSubjectId: [
      {
        required: true,
        message: '业务成本科目不能为空',
      },
    ],
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',

            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      // 禁用状态的修改
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'MOVE_DOWN': {
            const targetParentId = row.parentId;

            const tableData = tableOptions.data.filter(
              (v: any) => v.parentId === targetParentId && v.isActive !== false,
            );
            const targetIdx = tableData.findIndex((v: any) => v.id === row.id);

            item.disabled =
              !actionPermissions.apUpdate || targetIdx === tableData.length - 1;
            break;
          }
          case 'MOVE_UP': {
            const targetParentId = row.parentId;
            const tableData = tableOptions.data.filter(
              (v: any) => v.parentId === targetParentId && v.isActive !== false,
            );
            const targetIdx = tableData.findIndex((v: any) => v.id === row.id);
            item.disabled = !actionPermissions.apUpdate || targetIdx === 0;
            break;
          }
        }
      });
      // 隐藏状态的修改
      return !(row.internal || row.parentId || !localEditable.value);
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row, column }: any) {
      // 如果是内置数据则不可修改
      if (row.internal) {
        return;
      }
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前没有修改权限');
        return;
      }

      if (column.field === 'allocationQuantity' && row.parentId) {
        return;
      }

      // 版本数据已启用无法进行编辑
      if (!localEditable.value) {
        ElMessage.warning('当前数据已提交,不可编辑');
        return false;
      }
      return true;
    },
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: false,
    reserve: true,
  },
  columns,
  data: [],
  showFooter: true,
  footerData: [
    {
      id: null,
      materialName: '合计',
      internal: true,

      requisitionAmount: 0,
    },
  ],
});

// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnits
      ? row.optionalUnits.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem);
  },
  async menuClick({
    menu,
    rowIndex,
    row,
  }: {
    menu: any;
    row: any;
    rowIndex: any;
  }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await deleteRequisitionDetail(id);
        if (res) {
          await refreshData();
          ElMessage.success('删除成功');

          // 计算统计数据
          countTotalPrice();
        }
      });
    }
    if (menu.code === 'MOVE_DOWN') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex + 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveRequisitionDetail(params);

      refreshData();
    }
    if (menu.code === 'MOVE_UP') {
      const fromId = tableOptions.data[rowIndex]?.id;
      const toId = tableOptions.data[rowIndex - 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveRequisitionDetail(params);

      refreshData();
    }
  },
  // 完成编辑
  async editClosed({ row }: any) {
    const errMsg = await tableRef.value.validate(row);
    if (errMsg) return;

    // 计算统计数据
    const data = {
      id: row.id,
      actualQuantity: row.actualQuantity,
      remark: row.businessCostSubjectDetailId,
    };
    const res = await editRequisitionDetail(data);

    countTotalPrice();
    if (res) {
      refreshData();
    }
  },
};

// 刷新数据
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

async function jumpTo(row: any) {
  const routeName = route.name as string;
  pageReturnState.setReturnState({
    fromPage: routeName,
    recordId: localInfoData.value?.id ?? '',
    isDialogOpen: true,
  });

  // 跳转收料单
  if (row.detailCode.includes('收')) {
    router.push({
      name: 'MenuMaterialReceivingForm',
      query: { code: row.detailCode },
    });
  }
  // 跳转xxx
  if (row.detailCode.includes('退')) {
    router.push({
      name: 'MenuMaterialReturnInventoryForm',
      query: { code: row.detailCode },
    });
  }
}

// 确认提交
async function insureSubmit() {
  if (localInfoData.value.auditStatus === AuditStatus.APPROVED) {
    ElMessage.warning('当前数据已通过审批,不可编辑');
    return;
  }
  if (localInfoData.value.auditStatus === AuditStatus.AUDITING) {
    ElMessage.warning('当前数据审批中,不可编辑');
    return;
  }

  if (localInfoData.value.submitStatus === SubmitStatus.PENDING) {
    // 需要提交数据了
    const isPassForm = checkForm();
    if (!isPassForm) {
      return;
    }

    if (tableOptions.data.length <= 1) {
      ElMessage.warning('请添加验收材料');
      return;
    }

    const $grid = tableRef.value;
    if ($grid) {
      const errMap = await $grid.validate(true);
      if (errMap) return true;
    }
  }

  const submitStatus =
    localInfoData.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const submitStatusText =
    localInfoData.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  const { id } = formData.value;

  const data = {
    id,
    submitStatus, // 修改提交状态为已提交
  };

  const res = await editRequisitionBill(data);
  if (res) {
    localInfoData.value.submitStatus = submitStatus;

    ElMessage.success(`${submitStatusText}成功`);
    emit('refresh');
  }
}

// 确认修改表单
async function insureSave() {
  const requisitionDate = dayjs(formData.value.requisitionDate);
  const year = requisitionDate.year();
  const month = requisitionDate.month() + 1;
  const day = requisitionDate.date();

  const data = {
    id: formData.value.id,
    supplierId: formData.value.supplierId,
    departmentName: formData.value.departmentName,
    partName: formData.value.partName,
    year,
    month,
    day,
  };

  await editRequisitionBill(data);
}

// 计算合计的 含税单价 和 单价
function countTotalPrice() {
  const totalItem = tableOptions.footerData[0];
  let requisitionAmount = 0;
  const filterData = tableOptions.data.filter((item: any) => !item.internal);
  filterData.forEach((item: any) => {
    // 总金额
    if (item.amount && !item.parentId) {
      requisitionAmount = Big(requisitionAmount).plus(item.amount).toNumber();
    }
  });

  totalItem.amount = requisitionAmount;
}

// 获取领料单位下拉数据
const supplierOptions = ref<any[]>([]);
const supplierOptionsMemo = ref<any[]>([]);
async function getSupplierOptions() {
  supplierOptionsMemo.value = [];
  const suppliers = await getRequisitionDepartmentList();
  for (const item of suppliers) {
    supplierOptionsMemo.value.push({
      label: item.name,
      value: item.id,
    });
  }
}

// 领料单位发生变更时
async function handleDepartmentChange() {
  formData.value.departmentName = supplierOptions.value.find(
    (item) => item.value === formData.value.supplierId,
  )?.label;

  await insureSave();
}

// ElSelect，下拉框展开收起事件
async function handleSelectVisibleChange(data: boolean) {
  supplierOptions.value = data
    ? supplierOptionsMemo.value
    : [
        {
          value: formData.value.supplierId,
          label: formData.value.departmentName,
        },
      ];
}

async function handleBusinessCostSubjectChange(row: any) {
  console.log(row);
}

function handleBusinessCostSubjectVisibleChange({ data: boolean }) {
  if (data) {
    businessCostSubjectSelectOptions.value = [];

    const row = tableRef.value.getCurrentRow() as any;
    for (const item of businessCostSubjectList) {
      if (row.materialId === item.materialId) {
        businessCostSubjectSelectOptions.value.push({
          label: item.name,
          value: item.businessCostSubjectDetailId,
        });
      }
    }
  }
}

// 弹窗打开回调
async function handleOpen() {
  // 获取领料单位
  await getSupplierOptions();
  // 默认展示options
  supplierOptions.value = [
    {
      value: formData.value.supplierId,
      label: formData.value.departmentName,
    },
  ];

  // 获取业务成本科目map，用于展示
  businessCostSubjectList.value = await getBusinessCostSubjectList(
    formData.value.id,
  );

  // 获取项目参数设置
  await getOrgParams();
}

// 基础单据时间是否可修改设置
const modifyAccountTime = ref(false);
async function getOrgParams() {
  const orgParams = await getIsEditParams(ParamsNamesType.BASE_ACCOUNT);
  modifyAccountTime.value =
    orgParams[ParamsNamesType.BASE_ACCOUNT].modifyAccountTime;
}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
  emit('refresh');
}

// 传递清单列表
const goodList = ref([]);
provide('goodList', goodList);

// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const res = await getRequisitionDetailList(id);
  tableOptions.data =
    res.length === 0 ? [addBtnBlockItem] : [...res, addBtnBlockItem];

  countTotalPrice();
}

// 点击穿梭框
async function transferDataClick() {
  if (!checkForm()) {
    return;
  }

  curTab.value = TabEnum.MATERIAL_ENTRY;

  addMaterialinfoData.value = {
    requisitionFormId: formData.value.id,
    materialSearchType: curTab.value,
  };

  addOrEditMaterialVisible.value = true;
}

function checkForm() {
  return true;
}

// 关闭点击
async function closeClick() {
  if (localEditable.value) {
    emit('refresh');
  }

  drawerVisible.value = false;
}

// 获取附件列表
async function getAnnexlist() {
  const id = localInfoData.value.id;
  const res = await getRequisitionAttachmentList(id);

  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);

  fileList.value = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
}
// 新增附件
async function addAnnex(data: any) {
  const _data = {
    requisitionFormId: localInfoData.value.id,
    fileName: data.fileName,
    fileKey: data.fileKey,
    fileSize: Number(data.fileSize),
    fileExt: data.fileExt,
    fileContentType: data.fileContentType,
  };

  const res = await addRequisitionAttachment(_data);
  if (res) {
    ElMessage.success('添加成功');
  }
}
// 移除附件
async function removeAnnex(data: any) {
  const id = data.id;
  const res = await delRequisitionAttachment(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}

async function init() {}

onBeforeMount(() => {
  init();
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}

.info {
  ::v-deep(.el-form-item--large) {
    margin-bottom: 0;
  }
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
.el-form-item--large {
  margin-bottom: 12px;
}
</style>
