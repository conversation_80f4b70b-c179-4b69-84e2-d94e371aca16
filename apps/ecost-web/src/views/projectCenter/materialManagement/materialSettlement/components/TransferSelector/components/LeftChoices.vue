<template>
  <div class="selections h-full">
    <div class="flex h-[30px] gap-x-10 text-[14px]">
      <span>收料单/退货单</span>
      <ElButton type="primary" size="small" @click="filter">
        重置筛选
      </ElButton>
    </div>
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
            <!-- <template #code="{ row }">
              <ElButton type="text" size="small">{{ row.code }}</ElButton>
            </template> -->
            <template #purchaseType="{ row }">
              <div>
                {{ getPurchaseTypeLabel(row.purchaseType) }}
              </div>
            </template>
            <template #submitStatus="{ row }">
              <div
                :class="{
                  'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                  'text-red-500': row.submitStatus === SubmitStatus.PENDING,
                }"
              >
                {{ getSubmitStatusLabel(row.submitStatus) }}
              </div>
            </template>
            <template #auditStatus="{ row }">
              <div
                :class="{
                  'text-red-500': row.auditStatus === AuditStatus.PENDING,
                  'text-orange-500':
                    row.auditStatus === AuditStatus.AUDITING ||
                    row.auditStatus === AuditStatus.REJECTED,
                  'text-green-500': row.auditStatus === AuditStatus.APPROVED,
                }"
              >
                {{ getAuditStatusLabel(row.auditStatus) }}
              </div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid ref="tableRef" v-bind="tableOptions">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, watch } from 'vue';

import { ElButton } from 'element-plus';

import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getPurchaseTypeLabel,
  getSubmitStatusLabel,
  purchaseTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import {
  amountFormat,
  resetFilter,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[]; // 已选的明细数据
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'selectAll', data: any): void;

  (e: 'add', data: any): void; // 添加到已选明细的 数据
}>();

const curTab = inject<any>('curTab'); // 当前选中的tab

// tab项
const tabOptions = ref([
  {
    label: '选择进场验收单材料',
    value: 'MATERIAL_ENTRY',
    disabled: true,
    visible: true,
  },
  { label: '选择合同材料', value: 'CONTRACT', disabled: true, visible: true },
]);
tabOptions.value.forEach((item) => {
  item.disabled = item.value !== curTab.value;
});

// 当前选中的分类表格数据
const currentClassItem = ref();
// 分类表格数据
const prevTableRef = ref();
// 分类表格配置
const prevColumns = [
  {
    field: 'seq',
    width: '20',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'code',
    title: '单据编码',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'purchaseType',
    title: '采购类型',
    minWidth: '80',
    slots: {
      default: 'purchaseType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: purchaseTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择采购类型',
      },
    },
  },
  {
    field: 'materialCategories',
    title: '材料类别',
    width: '90',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入材料类别',
      },
    },
  },
  {
    field: 'taxExcludedAmount',
    title: '金额',
    minWidth: '100',

    formatter: amountFormat,
  },
  {
    field: 'creator',
    title: '编制人',
    width: '70',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人',
      },
    },
  },
  {
    field: 'billDate',
    title: '收料日期',
    width: '100',
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '100',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审核状态',
    width: '100',
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
];
const prevTableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-300';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns: prevColumns,
  data: props.choiceClassData,
});
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = nval;
    if (prevTableOptions.data.length > 0) {
      const currentRow = prevTableOptions.data[0];
      currentClassItem.value = currentRow;
      setCurrentRow(prevTableOptions.data, prevTableRef.value, currentRow);
      emit('select', currentRow);
    }
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    currentClassItem.value = row;
    emit('select', row);
  },
  cellDblclick({ row }: any) {
    if (row.disabled || row.selected) {
      tableOptions.data = [];
      const $grid = prevTableRef.value;
      if ($grid) {
        $grid.clearCurrentRow();
      }
      return; // 如果是禁用状态则不可选
    }
    currentClassItem.value = row;
    row.selected = true;

    emit('selectAll', row);
  },
};

// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    file: 'seq',
    width: '30',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '100',
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: '80',
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'quantity',
    title: '收料数量',
    width: '80',
  },
  {
    field: 'priceExcludingTax',
    title: '收料单价(不含税)',
    width: '120',
    formatter: amountFormat,
  },
  {
    field: 'taxExcludedAmount',
    title: '收料金额',
    width: '120',
    formatter: amountFormat,
  },
  {
    field: 'remarks',
    title: '备注',
    width: '80',
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-300';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns,
  data: props.choiceDetailData,
});
// 表格数据赋值
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

async function filter() {
  const $grid = prevTableRef.value;
  resetFilter(prevTableOptions.columns, $grid);
}
</script>
