<template>
  <ElDialog
    v-model="dialogVisible"
    :destroy-on-close="false"
    :title="title"
    @close="dialogClosed"
    top="2%"
    :style="{ width: '80%' }"
  >
    <TransferSelector
      v-model:selection-data="selectionData"
      :choice-class-data="choiceClassData"
      :choice-detail-data="choiceDetailData"
      :cur-tab="curTab"
      @select="classSelect"
      @select-all="classSelectAllItem"
    />

    <template #footer>
      <ElButton @click="dialogClosed">取消</ElButton>
      <ElButton type="primary" @click="submit">确定</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { inject, onBeforeMount, ref, watch } from 'vue';

import { add } from '@ewing/infra-cloud-sdk/dist/common-utils';
import { ElButton, ElDialog, ElMessage } from 'element-plus';

import {
  getReceivingReturnDetail,
  getReceivingReturnList,
  updateSettlementDetails,
} from '#/api/projectCenter/materialManagement/materialSettlement';
import { AuditStatus } from '#/types/materialManagement';

import TransferSelector from './TransferSelector/index.vue';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}

const props = withDefaults(
  defineProps<{
    infoData: {
      contractId: string;
      settlementId: string;
      supplierId: string;
    };
    title: string;
    visible: boolean;
  }>(),
  {
    title: '',
    visible: false,
    infoData: () => {
      return {
        settlementId: '',
        contractId: '',
        supplierId: '',
      };
    },
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

// 当前选择的数据
const curTab = inject<any>('curTab');

// 选择区的分类数据
const choiceClassData = ref<any>([]);
// 选择区的明细数据
const choiceDetailData = ref<any>([]);
// 确认区的数据
const selectionData = inject<any>('goodList');
// 传递的表单
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval) => {
    localInfoData.value = nval;
    // 每次传值的时候调用分类列表
    await getCategoryList();
    initSelectionData();
  },
  { deep: true },
);
// 弹窗是否展示
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);
// 获取分类列表
async function getCategoryList() {
  if (!localInfoData.value.settlementId) return;
  const res = await getReceivingReturnList(localInfoData.value);
  choiceClassData.value = res.map((item: any) => {
    return {
      ...item,
      disabled: item.auditStatus !== AuditStatus.APPROVED || item.isSelected,
      selected: item.isSelected,
    };
  });
}

function initSelectionData() {
  const totalItem = {
    id: -1,
    code: '合计',
    taxExcludedAmount: '',
    disabled: true,
  };
  selectionData.value = [totalItem];
  choiceClassData.value.forEach((item: any) => {
    if (item.isSelected) {
      totalItem.taxExcludedAmount = add(
        item.taxExcludedAmount,
        totalItem.taxExcludedAmount,
      )
        .toNumber()
        .toFixed(2);
      selectionData.value.push({
        ...item,
        disabled: true,
      });
    }
  });
}

async function classSelect(row: any) {
  const settlementId = localInfoData.value.settlementId;

  const receivingReturnIds = [];
  if (row.isSelection && row.code === '合计') {
    selectionData.value.forEach((item: any, index: number) => {
      if (index > 0) {
        receivingReturnIds.push(item.id);
      }
    });
  } else {
    receivingReturnIds.push(row.id);
  }

  if (receivingReturnIds.length === 0) {
    choiceDetailData.value = [];
    return;
  }

  const res = await getReceivingReturnDetail({
    settlementId,
    receivingReturnIds,
  });

  choiceDetailData.value = res.map((item: any, index: number) => {
    item.id = index + 1;

    return item;
  });
}

async function classSelectAllItem(row: any) {
  const settlementId = localInfoData.value.settlementId;
  const res = await getReceivingReturnDetail({
    settlementId,
    receivingReturnIds: [row.id],
  });
  choiceDetailData.value = res.map((item: any, index: number) => {
    item.id = index + 1;

    return item;
  });
  const totalItem = selectionData.value[0];
  totalItem.taxExcludedAmount = add(
    row.taxExcludedAmount,
    totalItem.taxExcludedAmount,
  )
    .toNumber()
    .toFixed(2);
  selectionData.value.push({
    ...row,
  });
}

// 关闭弹窗
function dialogClosed() {
  selectionData.value = [];
  choiceDetailData.value = [];
  emit('update:visible', false);
}

// 提交
const submit = async () => {
  const filterSelectionData = selectionData.value.filter(
    (v: any) => !v.disabled,
  );

  const settlementId = localInfoData.value.settlementId;
  const receivingReturnIds = filterSelectionData.map((item: any) => {
    return item.id;
  });

  if (receivingReturnIds.length <= 0) {
    ElMessage.warning('请先添加单据');
    return;
  }

  const res = await updateSettlementDetails({
    settlementId,
    receivingReturnIds,
  });

  if (res) {
    ElMessage.success('添加成功');
    emit('refresh');
    emit('update:visible', false);
  }
};
// 初始化
async function init() {}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss"></style>
