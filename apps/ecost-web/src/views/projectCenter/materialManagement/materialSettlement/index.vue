<template>
  <Page class="ml-4 mt-4 h-full rounded bg-white">
    <div class="flex h-full">
      <!-- 左侧统计金额、合同数、材料列表 -->
      <div class="area-left h-full w-[20%] pr-2">
        <div class="flex items-center">
          <span style="font-size: 14px">选择统计账期</span>
          <ElSelect
            class="ml-1"
            style="width: 120px"
            v-model="period"
            placeholder="请选择"
            @change="periodSelect"
            clearable
          >
            <ElOption
              v-for="valueList in periodList"
              :key="valueList.value"
              :label="valueList.label"
              :value="valueList.value"
            />
          </ElSelect>
        </div>
        <div class="mt-4 flex items-center">
          <ElDescriptions title="结算金额" :column="1">
            <ElDescriptionsItem label="本期结算金额(元):">
              <ElButton
                size="large"
                type="primary"
                link
                @click="getSettlementList(periodSettlementData)"
              >
                {{
                  amountFormatValue(periodSettlementData.taxIncludedAmount || 0)
                }}
              </ElButton>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="开累结算金额(元):">
              <ElButton
                size="large"
                type="primary"
                link
                @click="getSettlementList(periodSettlementData, true)"
              >
                {{
                  amountFormatValue(
                    periodSettlementData.cumulationTaxIncludedAmount || 0,
                  )
                }}
              </ElButton>
            </ElDescriptionsItem>
          </ElDescriptions>
        </div>
        <div class="bar-chart-container">
          <div class="chart-title">
            <p>本期合同结算数量({{ settlementPeriodContract.periodStr }})</p>
          </div>
          <ElRow :gutter="24" align="bottom" class="chart-row">
            <ElCol
              :span="8"
              v-for="(item, index) in settlementPeriodContract.data"
              :key="index"
            >
              <div class="bar-container">
                <div class="bar" :style="{ height: `${item.height}px` }"></div>
                <div class="bar-label">{{ item.name }}</div>
                <div class="bar-value">{{ item.value }}</div>
              </div>
            </ElCol>
          </ElRow>
        </div>
        <div style="font-size: 14px" class="mt-4 font-bold">材料结算金额</div>
        <div class="mt-2 h-[calc(100%-400px)]">
          <VxeGrid class="material-table" v-bind="materialTableOptions">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>

            <template #taxIncludedAmount="{ row }">
              <div>
                <ElButton
                  size="small"
                  type="primary"
                  link
                  @click="getSettlementList(row)"
                >
                  {{ amountFormatValue(row.taxIncludedAmount) }}
                </ElButton>
              </div>
            </template>

            <template #cumulationTaxIncludedAmount="{ row }">
              <div>
                <ElButton
                  size="small"
                  type="primary"
                  link
                  @click="getSettlementList(row, true)"
                >
                  {{ amountFormatValue(row.cumulationTaxIncludedAmount) }}
                </ElButton>
              </div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 右侧列表 -->
      <div class="area-right h-full flex-1 overflow-auto">
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <!-- 新增 重置 导出相关操作 -->
          <template #top>
            <div class="flex h-[48px] items-center justify-between gap-4">
              <div class="mb-2 flex items-center gap-2">
                <ElButton
                  type="primary"
                  size="small"
                  @click="debounceAddPurchaseData"
                  v-auth="actionPermissions.apCreate"
                >
                  新增采购结算
                </ElButton>
                <ElButton
                  type="primary"
                  size="small"
                  v-auth="actionPermissions.apCreate"
                >
                  新增商砼结算
                </ElButton>
                <ElButton
                  type="primary"
                  size="small"
                  v-auth="actionPermissions.apCreate"
                >
                  新增租赁结算
                </ElButton>
                <ElButton
                  type="primary"
                  size="small"
                  v-auth="actionPermissions.apCreate"
                >
                  新增调拨结算
                </ElButton>
                <ElButton type="default" size="small" @click="filter">
                  重置筛选
                </ElButton>
              </div>
              <div class="flex items-center gap-4">
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出单据
                  </ElButton>
                </div>
                <div class="mb-2">
                  <ElButton
                    type="default"
                    size="small"
                    v-auth="actionPermissions.apExport"
                  >
                    导出列表
                  </ElButton>
                </div>
              </div>
            </div>
          </template>

          <!-- 行号 -->
          <template #seq="{ $rowIndex }">
            <div>{{ $rowIndex + 1 }}</div>
          </template>

          <!-- 单据编码 -->
          <template #code="{ row }">
            <div>
              <ElButton
                size="small"
                type="primary"
                link
                @click="openDetail(row)"
              >
                {{ row.code }}
              </ElButton>
            </div>
          </template>

          <!-- 结算类型 -->
          <template #settlementType="{ row }">
            <div>
              {{ getSettlementTypeLabel(row.settlementType) }}
            </div>
          </template>

          <!-- 结算日期 -->
          <template #settlementDate="{ row }">
            <div>
              {{ dayjs(`${row.settlementDate}`).format('YYYY-MM-DD') }}
            </div>
          </template>

          <template #submitStatus="{ row }">
            <div
              :class="{
                'text-green-500': row.submitStatus === SubmitStatus.SUBMITTED,
                'text-red-500': row.submitStatus === SubmitStatus.PENDING,
              }"
            >
              {{ getSubmitStatusLabel(row.submitStatus) }}
            </div>
          </template>
          <template #auditStatus="{ row }">
            <div
              :class="{
                'text-red-500': row.auditStatus === AuditStatus.PENDING,
                'text-orange-500':
                  row.auditStatus === AuditStatus.AUDITING ||
                  row.auditStatus === AuditStatus.REJECTED,
                'text-green-500': row.auditStatus === AuditStatus.APPROVED,
              }"
            >
              {{ getAuditStatusLabel(row.auditStatus) }}
            </div>
          </template>
          <template #qrcode="{ row }">
            <ElPopover
              placement="left"
              trigger="click"
              v-if="row.submitStatus === SubmitStatus.SUBMITTED"
            >
              <div>
                <QrcodeVue :value="row.code" :size="120" />
              </div>
              <template #reference>
                <div class="flex justify-center">
                  <QrcodeVue :value="row.code" :size="28" />
                </div>
              </template>
            </ElPopover>
          </template>
        </VxeGrid>
      </div>
    </div>

    <EditDrawer
      v-model:visible="drawerVisible"
      :info-data="infoData"
      :editable="editable"
      @move="contractSelectMove"
      @refresh="refreshData"
    />
  </Page>
</template>

<script lang="ts" setup>
import type { getMaterialSettlementListType } from '#/api/projectCenter/materialManagement/materialSettlement';

import { onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { add } from '@ewing/infra-cloud-sdk/dist/common-utils';
import dayjs from 'dayjs';
import {
  ElButton,
  ElCol,
  ElDescriptions,
  ElDescriptionsItem,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElPopover,
  ElRow,
  ElSelect,
} from 'element-plus';
import _ from 'lodash';
import QrcodeVue from 'qrcode.vue';

import {
  addMaterialSettlement,
  changeAuditStatus,
  delInspectionBill,
  getMaterialSettlementList,
  getMaterialSettlementPeriodList,
  getSettlementPeriodAmounts,
  getSettlementPeriodContracts,
  getSettlementPeriodMaterials,
} from '#/api/projectCenter/materialManagement/materialSettlement';
import {
  AuditStatus,
  auditStatusOption,
  getAuditStatusLabel,
  getSettlementTypeLabel,
  getSubmitStatusLabel,
  settlementTypeLabelOption,
  SubmitStatus,
  submitStatusOption,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import {
  amountFormat,
  amountFormatValue,
  findNextOrPrevRow,
  resetFilter,
  setCurrentRow,
  vxeBaseConfig,
} from '#/utils/vxeTool';

import EditDrawer from './components/EditDrawer.vue';

const { actionPermissions } = getCurrentPremission();
const debounceAddPurchaseData = _.debounce(addData.bind(null, 'PURCHASE'), 500);
const debounceAddAllocationFromData = _.debounce(
  addData.bind(null, 'ALLOCATION_FROM'),
  500,
);
const debounceAddConcreteData = _.debounce(addData.bind(null, 'CONCRETE'), 500);
const debounceAddRentalTurnoverData = _.debounce(
  addData.bind(null, 'RENTAL_TURNOVER'),
  500,
);

// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '35',
    fixed: 'left',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'code',
    title: '单据编码',
    width: '110',
    fixed: 'left',
    slots: {
      default: 'code',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'settlementType',
    title: '结算类型',
    width: '80',
    fixed: 'left',
    filters: [{ data: '' }],
    slots: {
      default: 'settlementType',
    },
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: settlementTypeLabelOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择结算类型',
      },
    },
  },
  {
    field: 'newSupplierName',
    title: '供应商名称',
    minWidth: '150',
    fixed: 'left',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'contractName',
    title: '合同名称',
    minWidth: '120',
    fixed: 'left',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'priceType',
    title: '价格类型',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入价格类型',
      },
    },
    width: '80',
  },
  {
    field: 'taxExcludedAmount',
    title: '本期结算金额(不含税)',
    minWidth: '90',

    formatter: amountFormat,
  },
  {
    field: 'taxIncludedAmount',
    title: '本期结算金额(含税)',
    minWidth: '90',

    formatter: amountFormat,
  },
  {
    field: 'taxAmount',
    title: '本期可抵扣税金',
    minWidth: '90',

    formatter: amountFormat,
  },
  {
    field: 'creator',
    title: '编制人',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入编制人',
      },
    },
    width: '70',
  },
  {
    field: 'settlementDate',
    title: '结算日期',
    width: '80',
    slots: {
      default: 'settlementDate',
    },
  },
  {
    field: 'submitStatus',
    title: '提交状态',
    width: '80',
    slots: {
      default: 'submitStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: submitStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择提交状态',
      },
    },
  },
  {
    field: 'auditStatus',
    title: '审批状态',
    width: '100',
    editRender: {
      name: 'VxeSelect',
      options: auditStatusOption,
    },
    slots: {
      default: 'auditStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: auditStatusOption, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择审批状态',
      },
    },
  },
  {
    title: '二维码',
    width: '80',
    slots: {
      default: 'qrcode',
    },
  },
];

const materialColumns = [
  {
    file: 'seq',
    title: '',
    width: '30',
    slots: {
      default: 'seq',
    },
    treeNode: true,
  },
  {
    field: 'materialCategoryName',
    title: '材料类别',
    width: '70',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入材料类别',
      },
    },
  },
  {
    field: 'taxIncludedAmount',
    title: '本期结算金额',
    minWidth: '80',
    slots: {
      default: 'taxIncludedAmount',
    },

    sortable: true,
  },
  {
    field: 'cumulationTaxIncludedAmount',
    title: '开累结算金额',
    minWidth: '100',
    slots: {
      default: 'cumulationTaxIncludedAmount',
    },

    sortable: true,
  },
];

// 左侧数据
const periodSettlementData = ref({
  taxIncludedAmount: '',
  cumulationTaxIncludedAmount: '',
});

const settlementPeriodContract = ref({
  data: [
    {
      name: '应结算',
      value: 0,
      height: 0,
    },
    {
      name: '已办理',
      value: 0,
      height: 0,
    },
    {
      name: '未办理',
      value: 0,
      height: 0,
    },
  ],
  periodStr: '',
});

const materialTableOptions = reactive<any>({
  ...vxeBaseConfig,
  cellConfig: {
    height: 30,
  },
  columns: materialColumns,
  data: [],
});

async function getSettlementList(row: any, isCumulation = false) {
  const opts = {
    materialCategoryId: row.materialCategoryId,
    isCumulation,
  };
  await getList(opts);
  return null;
}

// 侧抽屉数据
const drawerVisible = ref(false);
const infoData = ref();
const editable = ref(true); // 是否可编辑

// 当前点击项
const currentItem = ref<any>();
const period = ref();
const periodList = ref<any>([]);

const tableRef = ref();

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ row, options }: any) => {
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled =
              !actionPermissions.apDelete ||
              row.submitStatus !== SubmitStatus.PENDING;
            break;
          }

          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row }: any) {
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前无修改权限');
        return;
      }
      // 版本数据已启用无法进行编辑
      if (row.submitStatus === SubmitStatus.PENDING) {
        ElMessage.warning('请先提交数据');
        return false;
      }
      return true;
    },
  },
  columns,
  data: [],
  showFooter: true,
  footerData: [
    {
      id: null,
      code: '合计',
      internal: true,

      taxExcludedAmount: 0,
      taxIncludedAmount: 0,
      taxAmount: 0,
    },
  ],
});
// 表格事件
const tableEvents = {
  cellClick({ row }: { row: any }) {
    currentItem.value = row;
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;

    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({ menu, row }: { menu: any; row: any }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionBill(id);
        if (res) {
          drawerVisible.value = false;
          await refreshData();
          ElMessage.success('删除成功');
        }
      });
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (column.field === 'auditStatus') {
      const id = row.id;
      const item = auditStatusOption.find(
        (item) => item.value === row.auditStatus,
      );

      ElMessageBox.confirm(`你确定要${item?.label}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const res = await changeAuditStatus(id, row.auditStatus);
          if (res) {
            refreshData();
            ElMessage.success(`${item?.label}成功`);
          }
        })
        .catch(async () => {
          refreshData();
        });
    }
  },
};

async function filter() {
  const $grid = tableRef.value;
  resetFilter(tableOptions.columns, $grid);
}

// 新增数据
async function addData(settlementType: string) {
  const res = await addMaterialSettlement({ settlementType });
  res.orgName = res.projectName;
  infoData.value = res;
  currentItem.value = res;
  await refreshData();
  drawerVisible.value = true;
}

// 打开详情
async function openDetail(row: any, isOpen = true) {
  row.orgName = row.projectName;
  infoData.value = row;
  if (isOpen) {
    drawerVisible.value = true;
  }
}

async function getList(opt?: any) {
  tableOptions.loading = true;

  const params: getMaterialSettlementListType = {};

  if (period.value) {
    const [year, month] = period.value.split('-');
    params.year = year;
    params.month = month;
    params.isCumulation = false;
  } else {
    params.isCumulation = true;
  }

  if (opt) {
    params.isCumulation = opt.isCumulation;
    params.materialCategoryId = opt.materialCategoryId;
  } else {
    const amountRes = await getSettlementPeriodAmounts(params);
    const materialAmountRes = await getSettlementPeriodMaterials(params);
    periodSettlementData.value = amountRes;

    materialAmountRes.forEach((item: any) => {
      item.id = item.materialCategoryId;
      item.parentId = '';
      return item;
    });
    materialTableOptions.data = materialAmountRes;
  }

  const res = await getMaterialSettlementList(params);

  tableOptions.data = res;
  tableOptions.loading = false;
  countTotal();
}

function countTotal(isFilter = false) {
  const totalItem = tableOptions.footerData[0];
  let taxIncludedAmount = 0;
  let taxExcludedAmount = 0;
  let taxAmount = 0;
  const $grid = tableRef.value;
  if ($grid) {
    const { tableData } = $grid.getTableData();
    const targetData = isFilter ? tableData : tableOptions.data;
    targetData
      .filter((v: any) => !v.internal)
      .forEach((item: any) => {
        if (item.taxIncludedAmount) {
          taxIncludedAmount = add(
            taxIncludedAmount,
            item.taxIncludedAmount,
          ).toNumber();
        }
        if (item.taxExcludedAmount) {
          taxExcludedAmount = add(
            taxExcludedAmount,
            item.taxExcludedAmount,
          ).toNumber();
        }
        if (item.taxAmount) {
          taxAmount = add(taxAmount, item.taxAmount).toNumber();
        }
      });
    totalItem.taxIncludedAmount = taxIncludedAmount.toFixed(2);
    totalItem.taxExcludedAmount = taxExcludedAmount.toFixed(2);
    totalItem.taxAmount = taxAmount.toFixed(2);
  }
}

// 账期选择
async function periodSelect(value: any) {
  period.value = value;
  await getList();
}

async function getPeriodList() {
  periodList.value = [];
  const res = await getMaterialSettlementPeriodList();
  res.forEach((item: any) => {
    periodList.value.push({
      label: `${item.year}年${item.month}月`,
      value: `${item.year}-${item.month}`,
    });
  });
}

// 合同选中移动
async function contractSelectMove(move: number) {
  const $grid = tableRef.value;
  const targetItem = findNextOrPrevRow($grid, move);
  if (targetItem) {
    tableEvents.cellClick({ row: targetItem });
    setCurrentRow(tableOptions.data, tableRef.value, targetItem);
    openDetail(targetItem, false);
  }
}

// 刷新
async function refreshData() {
  await getPeriodList();
  await getList();
  await getSettlementContracts();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
}

async function getSettlementContracts() {
  const ret = await getSettlementPeriodContracts();
  settlementPeriodContract.value.periodStr = `${ret.year}年${ret.month}月`;
  settlementPeriodContract.value.data = [];

  let height = 110;

  if (ret.allCount && ret.allCount < height) {
    height = 90;
  }

  settlementPeriodContract.value.data.push(
    {
      name: '应结算',
      value: ret.allCount,
      height: ret.allCount ? height : 0,
    },
    {
      name: '已办理',
      value: ret.settledCount,
      height: ret.settledCount
        ? Math.floor((ret.settledCount / ret.allCount) * height)
        : 0,
    },
    {
      name: '未办理',
      value: ret.unSettledCount,
      height: ret.unSettledCount
        ? Math.floor((ret.unSettledCount / ret.allCount) * height)
        : 0,
    },
  );
}

// 初始化
async function init() {
  await getPeriodList();
  await getList();
  await getSettlementContracts();
}

// 渲染前
onMounted(async () => {
  await init();
});
</script>

<style lang="scss" scoped>
::v-deep(.el-descriptions__title) {
  font-size: 14px;
  font-weight: 600;
}
::v-deep(.material-table .vxe-table--render-default) {
  font-size: 9px;
}
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}

.bar-chart-container {
  width: 100%;
  height: 200px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 20px;
}

.chart-row {
  height: 120px;
  align-items: flex-end;
}

.bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar {
  width: 30px;
  background: linear-gradient(to top, #fac858, #fac858);
  border-radius: 4px 4px 0 0;
  transition: height 0.5s;
}

.bar-label {
  margin-top: 8px;
  font-size: 12px;
}

.bar-value {
  margin-top: 4px;
  font-size: 12px;
  color: #188df0;
  font-weight: bold;
}
</style>
