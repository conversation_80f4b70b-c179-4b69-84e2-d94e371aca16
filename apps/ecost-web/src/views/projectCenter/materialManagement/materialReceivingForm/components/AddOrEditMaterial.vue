<template>
  <ElDialog
    v-model="dialogVisible"
    :destroy-on-close="false"
    :title="title"
    @close="dialogClose"
    top="2%"
    :style="{ width: '80%' }"
    @open="dialogOpen"
  >
    <TransferSelector
      v-model:selection-data="selectionData"
      :choice-class-data="choiceClassData"
      :choice-detail-data="choiceDetailData"
      :cur-tab="curTab"
      @select="classSelect"
      @select-all="classSelectAllItem"
    />

    <template #footer>
      <ElButton @click="dialogClose">取消</ElButton>
      <ElButton type="primary" @click="submit">确定</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import type { addContractCompilationType } from '#/api/enterpriseCenter/materialManagement/materialContract';

import { inject, onBeforeMount, ref, watch } from 'vue';

import { El<PERSON>utton, ElDialog, ElMessage } from 'element-plus';

import {
  addInspectionDetail,
  getIncomingBillsDetailslList,
  getIncomingBillslList,
} from '#/api/enterpriseCenter/materialReceivingForm/materialReceivingForm';
import { AuditStatus } from '#/types/materialManagement';

import TransferSelector from './TransferSelector/index.vue';

export interface addOrEditFormType extends addContractCompilationType {
  id?: null | string;
}

const props = withDefaults(
  defineProps<{
    infoData: {
      materialSearchType?: string;
      purchaseType: string;
      receivingId: string;
    };
    title: string;
    visible: boolean;
  }>(),
  {
    title: '',
    visible: false,
    infoData: () => {
      return {
        receivingId: '',
        materialSearchType: '',
        purchaseType: '',
      };
    },
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
}>();

// 当前选择的数据
const curTab = inject<any>('curTab');

// 选择区的分类数据
const choiceClassData = ref<any>([]);
// 选择区的明细数据
const choiceDetailData = ref<any>([]);
// 确认区的数据
const selectionData = inject<any>('goodList');
// 传递的表单
const localInfoData = ref(props.infoData);

watch(
  () => props.infoData,
  (nval) => {
    localInfoData.value = nval;
  },
  { deep: true, immediate: true },
);
// 弹窗是否展示
const dialogVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    dialogVisible.value = nval;
  },
  { deep: true, immediate: true },
);

// 打开弹窗
function dialogOpen() {
  // 每次打开弹窗时清空数据
  choiceClassData.value = [];
  choiceDetailData.value = [];
  selectionData.value = [];

  getCategoryList();
}

// 获取分类列表
async function getCategoryList() {
  if (!localInfoData.value.receivingId) return;
  const res = await getIncomingBillslList(localInfoData.value.receivingId);
  choiceClassData.value = res.map((item: any) => {
    return {
      ...item,
      disabled: item.auditStatus !== AuditStatus.APPROVED,
      selected: false,
    };
  });
}

async function classSelect(row: any) {
  const disabled = row.disabled;
  const incomingInspectionId = row.id;
  const receivingId = localInfoData.value.receivingId;

  const res = await getIncomingBillsDetailslList(
    incomingInspectionId,
    receivingId,
  );
  const ids = new Set(selectionData.value.map((v: any) => v.id));
  const data = res.map((item: any) => {
    const selected = !!ids.has(item.id);
    return {
      ...item,
      specificationModel: item.spec,
      meteringUnit: item.unit,
      selected,
      disabled,
    };
  });
  choiceDetailData.value = res ? data : [];
}

async function classSelectAllItem(row: any) {
  const incomingInspectionId = row.id;

  const receivingId = localInfoData.value.receivingId;
  const res = await getIncomingBillsDetailslList(
    incomingInspectionId,
    receivingId,
  );
  const ids = new Set(selectionData.value.map((v: any) => v.id));
  // 筛选出所有还没有选择的数据
  const insertData = res.filter((item: any) => {
    return !ids.has(item.id);
  });
  const data = res.map((item: any) => {
    return {
      ...item,
      specificationModel: item.spec,
      meteringUnit: item.unit,
      selected: true,
    };
  });
  choiceDetailData.value = res ? data : [];

  selectionData.value.push(...insertData);
}

// 关闭弹窗
function dialogClose() {
  selectionData.value = [];
  emit('update:visible', false);
}

// 提交
const submit = async () => {
  const filterSelectionData = selectionData.value.filter(
    (v: any) => !v.disabled,
  );

  const receivingId = localInfoData.value.receivingId;
  const data = filterSelectionData.map((item: any) => {
    return {
      materialIncomingInspectionDetailId: item.id,
      materialId: item.materialId,
      materialName: item.materialName,
      materialSpec: item.materialSpec,
      incomingUnit: item.incomingUnit,
      contractUnit: item.contractUnit,
      qualityStandard: item.qualityStandard,
      priceType: item.priceType,

      priceExcludingTax: item.priceExcludingTax,
      priceIncludingTax: item.priceIncludingTax,

      actualQuantity: item.actualQuantity,
      remark: item.remark,
    };
  });

  if (data.length <= 0) {
    ElMessage.warning('请先添加材料');
    return;
  }

  const res = await addInspectionDetail(receivingId, data);
  if (res) {
    ElMessage.success('添加成功');
    emit('refresh');
    emit('update:visible', false);
  }
};
// 初始化
async function init() {}

onBeforeMount(async () => {
  init();
});
</script>
<style scoped lang="scss"></style>
