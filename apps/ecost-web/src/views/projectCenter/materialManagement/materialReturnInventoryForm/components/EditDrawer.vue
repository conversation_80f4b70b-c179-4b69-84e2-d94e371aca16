<template>
  <div class="edit-drawer relative h-[100%]">
    <ElDrawer
      v-bind="$attrs"
      v-model="drawerVisible"
      :modal="false"
      :destroy-on-close="true"
      :append-to-body="true"
      :size="sizeNum"
      modal-class="pointer-events-none"
      class="pointer-events-auto"
      :with-header="false"
      @close="handleClose"
      @opened="handleOpen"
    >
      <div class="h-full w-full">
        <div class="header box-border w-full pl-4 pr-4">
          <div class="flex h-[60px] w-full items-center justify-between">
            <div class="header-left flex items-center justify-between"></div>
            <div class="header-right flex">
              <div class="btn-group ml-4 pr-4">
                <ElButton
                  type="primary"
                  size="default"
                  @click="insureSubmit"
                  :disabled="
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.APPROVED
                  "
                >
                  {{
                    localInfoData.submitStatus === SubmitStatus.PENDING
                      ? '提交'
                      : '取消提交'
                  }}
                </ElButton>
                <!-- <ElButton type="default" size="default" disabled>
                导出单据
              </ElButton> -->
              </div>
              <div class="flex">
                <IconifyIcon
                  @click="prevBtn"
                  class="icon-box mr-4 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-left-line"
                />
                <IconifyIcon
                  @click="nextBtn"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="majesticons:chevron-right-line"
                />
              </div>
              <div>
                <ElTooltip
                  :content="isFullScreen ? '收起' : '全屏'"
                  placement="bottom"
                >
                  <IconifyIcon
                    @click="isFullScreen = !isFullScreen"
                    class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                    :icon="
                      isFullScreen
                        ? 'majesticons:arrows-collapse-full'
                        : 'majesticons:arrows-expand-full-line'
                    "
                  />
                </ElTooltip>
              </div>
              <div>
                <IconifyIcon
                  @click="closeClick"
                  class="icon-box mr-5 text-3xl outline-none hover:cursor-pointer hover:text-[#006be6]"
                  icon="ep:close"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content h-[calc(100%-60px)] p-[24px] pb-0 pt-2">
          <div class="relative flex h-[68px] items-center justify-center">
            <div class="title flex text-[24px] font-bold">
              退库单 (
              <div
                :class="{
                  'text-red-500':
                    localInfoData.submitStatus === SubmitStatus.PENDING,
                  'text-orange-500':
                    localInfoData.auditStatus === AuditStatus.AUDITING ||
                    localInfoData.auditStatus === AuditStatus.REJECTED,
                  'text-green-500':
                    localInfoData.auditStatus === AuditStatus.APPROVED ||
                    localInfoData.submitStatus === SubmitStatus.SUBMITTED,
                }"
              >
                <!-- 如果是 待审核 ： 提交状态 ， 如果不是 ： 审核状态 -->
                {{
                  localInfoData.auditStatus === AuditStatus.PENDING
                    ? getSubmitStatusLabel(localInfoData.submitStatus)
                    : getAuditStatusLabel(localInfoData.auditStatus)
                }}
              </div>
              )
            </div>
            <div
              class="qrcode absolute right-20 flex items-center"
              v-if="localInfoData.submitStatus === SubmitStatus.SUBMITTED"
            >
              <QrcodeVue value="退库单" :size="80" />
              <!-- <div class="ml-4 text-sm">导出{{ 2 }}次</div> -->
            </div>
          </div>

          <div class="content flex h-[calc(100%-70px)] w-full flex-col">
            <ElForm
              :model="formData"
              class="top-form grid grid-cols-2 gap-x-20 gap-y-1 pb-1 pt-6"
            >
              <ElFormItem
                label="项目名称："
                label-width="106px"
                size="large"
                label-position="left"
              >
                {{ formData.orgName }}
              </ElFormItem>
              <ElFormItem
                label="单据编码："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElInput
                  size="large"
                  v-model="formData.code"
                  placeholder=""
                  disabled
                />
              </ElFormItem>
              <ElFormItem
                label="退料单位："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElSelect
                  size="large"
                  v-model="formData.purchaseType"
                  placeholder=""
                  clearable
                  @change="purchaseypeChange"
                  filterable
                  :disabled="!localEditable"
                >
                  <ElOption
                    v-for="v in purchaseTypeLabelOption"
                    :key="v.supplierId"
                    :label="v.supplierName"
                    :value="v.supplierId"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="收料日期："
                label-width="106px"
                size="large"
                label-position="left"
                required
              >
                <ElDatePicker
                  size="large"
                  v-model="formData.entryDate"
                  type="date"
                  placeholder=""
                  @change="entryDateChange"
                  :clearable="false"
                  :disabled="
                    !entryDateEdit ||
                    !localEditable ||
                    !actionPermissions.apUpdate
                  "
                />
              </ElFormItem>
            </ElForm>

            <div class="flex-1 overflow-auto">
              <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
                <template #seq="{ row, $rowIndex }">
                  <div v-if="row.id">{{ $rowIndex + 1 }}</div>
                </template>
                <template #materialName="{ row, $rowIndex }">
                  <div class="flex items-center justify-center">
                    <div class="flex">
                      <div v-if="row.parentId">
                        <ElButton
                          size="small"
                          type="primary"
                          @click="jumpTo(row)"
                          link
                        >
                          {{ row.materialName }}
                        </ElButton>
                      </div>
                      <div v-else class="pl-3">
                        {{ row.materialName }}
                      </div>
                      <div v-if="row.isAddButtonRow">
                        <ElButton
                          size="small"
                          @click="transferDataClick({ row })"
                          :disabled="
                            !localEditable ||
                            !formData.purchaseType ||
                            !dateDisAbled
                          "
                        >
                          +
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
                <template #canReversalQuantity="{ row }">
                  {{ row.canReversalQuantity }}
                </template>
                <template #depreciationAmount="{ row }">
                  <div v-if="row.returnInventoryType === 'SELF_OWNED_TURNOVER'">
                    {{ row.depreciationAmount }}
                  </div>
                </template>
                <template #reversalAmount="{ row }">
                  <div class="font-bold text-orange-500">
                    {{ amountFormat({ cellValue: row.reversalAmount }) }}
                  </div>
                </template>
                <template #taxExcludedAmount="{ row }">
                  <div class="font-bold text-orange-500">
                    {{ amountFormat({ cellValue: row.taxExcludedAmount }) }}
                  </div>
                </template>
              </VxeGrid>
            </div>

            <ElForm class="info grid grid-cols-3 gap-x-20 pt-2">
              <ElFormItem
                label="施工员："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="收料员："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="退料员："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.user }}
              </ElFormItem>
              <ElFormItem
                label="编制人："
                label-width="100px"
                size="large"
                label-position="left"
              >
                {{ formInfo.creator }}
              </ElFormItem>
            </ElForm>
          </div>

          <div class="footer flex items-center">
            <!-- <ElButton type="primary" size="default" @click="insureAduit">
            发起审核
          </ElButton> -->
          </div>
        </div>

        <ExtrasPanel
          :file-list="fileList"
          :visible-option="['ANNEX']"
          :editable="localEditable"
          list-type="picture"
          @del-annex="removeAnnex"
          @success-annex="addAnnex"
        />
      </div>

      <AddOrEditMaterial
        v-model:visible="addOrEditMaterialVisible"
        :info-data="addMaterialinfoData"
        title="出库材料"
        @refresh="refreshData"
      />
    </ElDrawer>
  </div>
</template>

<script lang="ts" setup>
import type { PurchaseTypeEnum } from '#/types/materialManagement';

import {
  computed,
  nextTick,
  onBeforeMount,
  provide,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { IconifyIcon } from '@vben/icons';

import Big from 'big.js';
import {
  dayjs,
  ElButton,
  ElDatePicker,
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
} from 'element-plus';
import QrcodeVue from 'qrcode.vue';

import { getFileCloudUrl } from '#/api/couldApi';
import {
  addAttachment,
  changeSubmitStatus,
  delAttachmen,
  delInspectionDetail,
  editInspectionBill,
  editReturnInventoryFormMaterialDetail,
  getAttachmentList,
  getInspectionDetailList,
  getSupplierList,
  moveInspectionDetail,
} from '#/api/enterpriseCenter/materialReturnInventoryForm/materialReturnInventoryForm';
import {
  getIsEditParams,
  ParamsNamesType,
} from '#/api/systemManagementApi/orgParams';
import ExtrasPanel from '#/components/ExtrasPanel/index.vue';
import { usePageReturnState } from '#/store';
import {
  AuditStatus,
  getAuditStatusLabel,
  getSubmitStatusLabel,
  SubmitStatus,
} from '#/types/materialManagement';
import { getCurrentPremission } from '#/utils/permission';
import { amountFormat, setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

import AddOrEditMaterial from './AddOrEditMaterial.vue';

defineOptions({
  name: 'MaterialEntryCheckEditDrawer',
});
const props = withDefaults(
  defineProps<{
    editable?: boolean;
    infoData: any;
    visible: boolean;
  }>(),
  {
    editable: true,
    visible: false,
    infoData: {},
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'move', payload: any): void;
}>();
const pageReturnState = usePageReturnState();
const route = useRoute();
const router = useRouter();
const { actionPermissions } = getCurrentPremission();
const extrasOpen = ref(false);
const extrasTab = ref('ANNEX');
// 收料日期是否可以编辑
const entryDateEdit = ref(false);
const purchaseTypeLabelOption = ref([]);

// 附件数据
const fileList = ref([]);

const traceColumns = [
  {
    field: 'name',
    title: '单据名称',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'entryNums',
    title: '进场数量',
    width: '60',
  },
  {
    field: 'actualNums',
    title: '实收数量',
    width: '60',
  },
];

// 是否展示弹窗
const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (nval) => {
    drawerVisible.value = nval;
  },
);
const curTab = ref();
provide('curTab', curTab);

// 表单数据
interface FormDataType {
  id: string;
  orgName: string;
  purchaseType: PurchaseTypeEnum | string;
  code: string;
  supplierId: string;
  oldSupplierName: string;
  supplierName: string;
  contractId: string;
  contractName: string;
  entryDate: string;
}

const formData = ref<FormDataType>({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  oldSupplierName: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
  returnInventoryType: '',
});
const oldFormData = ref({
  id: '',
  orgName: '',
  purchaseType: '',
  code: '',
  supplierId: '',
  oldSupplierName: '',
  supplierName: '',
  contractId: '',
  contractName: '',
  entryDate: '',
});
// 每次表单数据改变的时候就调用修改接口

// 底部展示数据
const formInfo = ref({
  user: '',
  creator: '',
});
// 全部的外层数据
const localInfoData = ref(props.infoData);
watch(
  () => props.infoData,
  async (nval: any) => {
    localInfoData.value = nval;

    // 调用获取材料清单列表
    await getList();
    // 调用获取附件列表
    await getAnnexlist();

    const { year, day, month, creator } = nval;

    const form = {
      id: nval.id,
      orgName: nval.orgName,
      supplierId: nval.supplierId,
      contractId: nval.contractId,
      purchaseType: nval.purchaseType || '',
      code: nval.code,
      oldSupplierName: nval.oldSupplierName,
      supplierName: nval.supplierName,
      contractName: nval.contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
      returnInventoryType: nval.returnInventoryType,
    };

    formData.value = form;
    if (form.oldSupplierName === form.supplierName) {
      formData.value.purchaseType = form.supplierId;
    }
    if (form.oldSupplierName !== form.supplierName) {
      formData.value.purchaseType = ref(form.oldSupplierName);
    }
    showDeprecitionNum.value =
      form.returnInventoryType !== 'SELF_OWNED_TURNOVER';
    oldFormData.value = structuredClone(form);
    formInfo.value = {
      user: '',
      creator,
    };
  },
);

const localEditable = computed(() => {
  return (
    localInfoData.value.submitStatus === SubmitStatus.PENDING &&
    actionPermissions.apUpdate
  );
});

watch(
  () => formData,
  (nval, oval: any) => {
    const { year, day, month } = oval;
    oldFormData.value = {
      id: oval.id,
      orgName: oval.orgName,
      supplierId: oval.supplierId,
      contractId: oval.contractId,
      purchaseType: oval.purchaseType || '',
      code: oval.code,
      oldSupplierName: oval.oldSupplierName,
      supplierName: oval.supplierName,
      contractName: oval.contractName,
      entryDate: dayjs(`${year}/${month}/${day}`).format('YYYY-MM-DD'),
    };
  },
);

// 退料单位改变
const purchaseypeChange = async () => {
  if (tableOptions.data.length > 2) {
    // 还原为旧值
    nextTick(() => {
      formData.value.purchaseType = oldFormData.value.purchaseType;
    });

    ElMessage.warning('请先删除材料数据后再修改');
  }

  await insureSave();
};
// 是否全屏
const isFullScreen = ref(false);
const sizeNum = computed(() => {
  return isFullScreen.value ? '100%' : '64%';
});

const addOrEditMaterialVisible = ref(false);

const addMaterialinfoData = ref();

// 内置节点数据
const addBtnBlockItem = {
  id: '',
  name: '',
  editable: false,
  disabled: false,
  isAddButtonRow: true, // 新增标识
};

const tableRef = ref();
const currentItem = ref();
const unitOptions = ref([]);
const showDeprecitionNum = ref(true);

const columns = [
  {
    field: 'seq',
    title: '',
    width: '50',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '140',
    slots: {
      default: 'materialName',
    },
    treeNode: true,
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    minWidth: '100',
  },
  {
    field: 'unit',
    title: '计量单位',
    minWidth: '100',
  },
  {
    field: 'canReversalQuantity',
    title: '可退数量',
    minWidth: '100',
    slots: {
      default: 'canReversalQuantity',
    },
  },
  {
    field: 'reversalQuantity',
    title: '退库数量',
    minWidth: '100',
    editRender: {
      name: 'input',
      props: {
        placeholder: '请输入退库数量',
      },
    },
  },
  {
    field: 'reversalUnit',
    title: '退库单价',
    minWidth: '100',
  },
  {
    field: 'depreciationAmount',
    title: '已折旧/摊销金额',
    minWidth: '100',
    visible: showDeprecitionNum,
    editRender: {
      name: 'input',
      props: {
        placeholder: '请输入金额',
      },
    },
  },
  {
    field: 'reversalAmount',
    title: '退库金额',
    minWidth: '100',

    slots: {
      default: 'reversalAmount',
      footer: 'reversalAmount',
    },
  },
  {
    field: 'businessCostSubjectDetailName',
    title: '成本科目挂接',
    minWidth: '100',
  },
  {
    field: 'partName',
    title: '原材料使用部位',
    minWidth: '120',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: '60',
    editRender: {
      name: 'input',
      props: {
        placeholder: '请输入备注',
      },
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  loading: false,
  cellClassName: ({ row, column }: any) => {
    const baseCalss = [];
    if (
      (!row.parentId &&
        !['remark', 'reversalQuantity'].includes(column.field)) ||
      (row.parentId && column.field !== 'depreciationAmount') ||
      !row.id
    ) {
      baseCalss.push('bg-gray-100 cursor-not-allowed');
    }
    return baseCalss.join(' ');
  },
  editRules: {
    reversalQuantity: [
      {
        validator({ cellValue, row }: any) {
          if (cellValue <= 0) {
            return new Error('退库数量必须大于零！');
          } else if (cellValue > row.canReversalQuantity) {
            return new Error(
              `退库数量不能大于库存数量${row.canReversalQuantity}`,
            );
          } else {
            return true;
          }
        },
      },
    ],
    depreciationAmount: [
      {
        validator({ cellValue, row }: any) {
          if (cellValue <= 0) {
            return new Error('已折旧/摊销金额必须大于零！');
          } else if (cellValue > row.requisitionPrice * row.reversalQuantity) {
            return new Error('已折旧/摊销金额大于领料金额，请重新填写！');
          } else {
            return true;
          }
        },
      },
    ],
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: false,
    reserve: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'MOVE_UP',
            name: '上移',

            prefixConfig: { icon: 'vxe-icon-arrows-up' },
            disabled: false,
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',

            prefixConfig: { icon: 'vxe-icon-arrows-down' },
            disabled: false,
          },
          {
            code: 'DELETE_ROW',
            name: '删除',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
            disabled: false,
          },
        ],
      ],
    },
    visibleMethod: ({ options, rowIndex, row }: any) => {
      options[0].forEach((item: any) => {
        item.disabled = false;
        switch (item.code) {
          case 'MOVE_DOWN': {
            const parentDate = tableOptions.data.filter(
              (v: any) => v.parentId === null && !v.internal,
            );
            const Index = parentDate.findIndex((v: any) => v.id === row.id);
            item.disabled = Index === parentDate.length - 1;
            break;
          }
          case 'MOVE_UP': {
            const parentDate = tableOptions.data.filter(
              (v: any) => v.parentId === null && !v.internal,
            );
            const Index = parentDate.findIndex((v: any) => v.id === row.id);
            item.disabled = Index === 0;
            break;
          }
        }

        if (!localEditable.value) {
          item.disabled = true;
        }
        if (row.parentId) {
          item.disabled = true;
        }
      });
      return !!row.id;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: true,
    beforeEditMethod({ row, column }: any) {
      if (!actionPermissions.apUpdate) {
        ElMessage.warning('您当前没有修改权限');
        return;
      }
      // 版本数据已启用无法进行编辑
      if (!localEditable.value || row.internal || !row.id) {
        return false;
      }
      // 父级：只允许编辑“退库数量”
      if (!row.parentId && !row.internal) {
        return ['remark', 'reversalQuantity'].includes(column.field);
      }
      // 子级：在自由周转的只允许编辑“已折旧”
      if (row.parentId) {
        return ['depreciationAmount'].includes(column.field);
      }

      return false;
    },
  },
  columns,
  data: [],
  showFooter: true,
  footerData: [
    {
      id: null,
      materialName: '合计',
      internal: true,

      reversalAmount: 0,
    },
  ],
});
// 表格事件
const tableEvents = {
  cellClick({ row, column }: { column: any; row: any }) {
    // 阻止内置行的点击事件
    if (row.internal) {
      return false;
    }
    currentItem.value = row;
    const optionalUnitArr = row?.optionalUnits
      ? row.optionalUnits.split(',')
      : [];
    const options = optionalUnitArr.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });

    unitOptions.value = options;

    // 阻止不可编辑单元格进入编辑状态
    if (
      (!row.parentId && !['reversalQuantity'].includes(column.field)) ||
      (row.parentId && column.field !== 'depreciationAmount') ||
      !row.id
    ) {
      return false;
    }
  },
  cellMenu({ row }: { row: any }) {
    currentItem.value = row;
    setCurrentRow(tableOptions.data, tableRef.value, currentItem.value);
  },
  async menuClick({
    menu,
    rowIndex,
    row,
  }: {
    menu: any;
    row: any;
    rowIndex: any;
  }) {
    if (menu.code === 'DELETE_ROW') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delInspectionDetail(id);
        if (res) {
          await refreshData();
          ElMessage.success('删除成功');

          // 计算统计数据
          countTotalPrice();

          // 关闭侧边栏同时清空数据
          extrasOpen.value = false;
        }
      });
    }
    if (menu.code === 'MOVE_DOWN') {
      const data = tableOptions.data.filter((item) => item.parentId == null);
      const fromId = data[rowIndex]?.id;
      const toId = data[rowIndex + 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      await getList();
      await nextTick(() => {
        if (tableRef.value && tableOptions.data) {
          const changeRightCurrent = tableOptions.data.find(
            (item: any) => item.id === row.id,
          );
          tableRef.value.setCurrentRow(changeRightCurrent);
        }
      });
    }
    if (menu.code === 'MOVE_UP') {
      const data = tableOptions.data.filter((item) => item.parentId == null);
      const fromId = data[rowIndex]?.id;
      const toId = data[rowIndex - 1]?.id;
      const params = {
        fromId,
        toId,
      };
      await moveInspectionDetail(params);

      await getList();
      await nextTick(() => {
        if (tableRef.value && tableOptions.data) {
          const changeRightCurrent = tableOptions.data.find(
            (item: any) => item.id === row.id,
          );
          tableRef.value.setCurrentRow(changeRightCurrent);
        }
      });
    }
  },
  // 完成编辑
  async editClosed({ row, column }: any) {
    if (row.returnInventoryType === 'CONSUME' && !row.reversalQuantity) {
      ElMessage.error(`退库数量不能为空！`);
      return;
    } else if (
      row.returnInventoryType === 'CONSUME' &&
      row.reversalQuantity * 1 <= 0
    ) {
      ElMessage.error(`退库数量不能小于零！`);
      return;
    } else if (
      row.returnInventoryType === 'CONSUME' &&
      row.reversalQuantity * 1 > row.canReversalQuantity
    ) {
      ElMessage.error(`退库数量不能大于库存数量！`);
      return;
    }
    if (
      row.returnInventoryType === 'SELF_OWNED_TURNOVER' &&
      !row.depreciationAmount
    ) {
      ElMessage.error(`已折旧/摊销金额不能为空！`);
      return;
    } else if (
      row.returnInventoryType === 'SELF_OWNED_TURNOVER' &&
      row.depreciationAmount * 1 <= 0
    ) {
      ElMessage.error(`已折旧/摊销金额不能小于零！`);
      return;
    } else if (
      row.returnInventoryType === 'SELF_OWNED_TURNOVER' &&
      row.depreciationAmount * 1 > row.requisitionPrice * row.reversalQuantity
    ) {
      ElMessage.error(`已折旧/摊销金额不能大于采购金额！`);
      return;
    }
    const data = {
      reversalQuantity: 0,
      depreciationAmount: 0,
      remark: '',
    };
    if (row.parentId) {
      data.depreciationAmount = row.depreciationAmount * 1;
    } else {
      data.reversalQuantity = row.reversalQuantity * 1;
      data.remark = row.remark;
    }
    const res = await editReturnInventoryFormMaterialDetail(row.id, data);
    if (res) {
      refreshData();
    }
  },
};

// 刷新数据
async function refreshData() {
  await getList();
  setCurrentRow(tableOptions.data, tableRef.value, currentItem);
}

// 确认提交
async function insureSubmit() {
  if (localInfoData.value.auditStatus === AuditStatus.APPROVED) {
    ElMessage.warning('当前数据已通过审批,不可编辑');
    return;
  }
  if (localInfoData.value.auditStatus === AuditStatus.AUDITING) {
    ElMessage.warning('当前数据审批中,不可编辑');
    return;
  }
  const id = localInfoData.value.id;
  const submitStatus =
    localInfoData.value.submitStatus === SubmitStatus.PENDING
      ? SubmitStatus.SUBMITTED
      : SubmitStatus.PENDING;
  const submitStatusText =
    localInfoData.value.submitStatus === SubmitStatus.PENDING ? '提交' : '取消';
  // const { id } = formData.value;

  const data = {
    submitStatus, // 修改提交状态为已提交
  };

  const res = await changeSubmitStatus(id, data);
  if (res) {
    localInfoData.value.submitStatus = submitStatus;

    ElMessage.success(`${submitStatusText}成功`);
    emit('refresh');
  }
}

const dateDisAbled = ref(true);

// 确认修改退库单
async function insureSave() {
  if (!formData.value.entryDate) {
    ElMessage.warning('请选择退货日期');
    return;
  }
  if (!formData.value.purchaseType) {
    ElMessage.warning('请选择退料单位');
    return;
  }
  const year = Number(dayjs(formData.value.entryDate).format('YYYY'));
  const month = Number(dayjs(formData.value.entryDate).format('M'));
  const day = Number(dayjs(formData.value.entryDate).format('D'));
  const supplierName = purchaseTypeLabelOption.value.find(
    (item) => item.supplierId === formData.value.purchaseType,
  )?.supplierName;
  const params = {
    supplierId: formData.value.purchaseType,
    supplierName,
    year,
    month,
    day,
  };

  await editInspectionBill(formData.value.id, params)
    .catch((error) => {
      if (error.code === 400) {
        dateDisAbled.value = false;
      }
    })
    .then((res) => {
      if (res) {
        dateDisAbled.value = true;
      }
    });
}

// 收料日期改变
async function entryDateChange() {
  await insureSave();
}
// 计算合计的含税单价 和 单价
function countTotalPrice() {
  const totalItem = tableOptions.footerData[0];
  // 重置合计金额
  totalItem.reversalAmount = 0;

  tableOptions.data.forEach((item: any) => {
    if (!item.parentId) {
      const amount = Number.parseFloat(item.reversalAmount) || 0;
      totalItem.reversalAmount = Big(totalItem.reversalAmount)
        .plus(amount)
        .round(2, Big.roundHalfUp)
        .toNumber();
    }
  });
}

// 弹窗打开回调
async function handleOpen() {}

// 弹窗关闭的回调
function handleClose() {
  emit('update:visible', false);
  emit('refresh');
}

// 传递清单列表
const goodList = ref([]);
provide('goodList', goodList);

// 获取表格数据
async function getList() {
  const { id } = localInfoData.value;

  const res = await getInspectionDetailList(id);
  tableOptions.data =
    res.length === 0 ? [addBtnBlockItem] : [...res, addBtnBlockItem];
  countTotalPrice();
}

// 点击穿梭框
async function transferDataClick() {
  addMaterialinfoData.value = {
    receivingId: formData.value.id,
    materialSearchType: curTab.value,
    purchaseType: formData.value.purchaseType,
  };

  addOrEditMaterialVisible.value = true;
}

// 关闭点击
async function closeClick() {
  if (localEditable.value) {
    emit('refresh');
  }

  drawerVisible.value = false;
}
// 新增附件
async function addAnnex(data: any) {
  const _data = {
    receivingId: localInfoData.value.id,
    fileName: data.fileName,
    fileKey: data.fileKey,
    fileSize: Number(data.fileSize),
    fileExt: data.fileExt,
    fileContentType: data.fileContentType,
  };

  const res = await addAttachment(_data);
  if (res) {
    ElMessage.success('添加成功');
    getAnnexlist();
  }
}
// 移除附件
async function removeAnnex(data: any) {
  const id = data.id;
  const res = await delAttachmen(id);
  if (res) {
    ElMessage.success('删除成功');
  }
}
// 获取附件列表
async function getAnnexlist() {
  const id = localInfoData.value.id;
  const res = await getAttachmentList(id);

  const fileKeys = res.map((v: any) => {
    return v.fileKey;
  });
  const urlData = await getFileCloudUrl(fileKeys);

  fileList.value = res.map((v: any) => {
    return {
      size: Number(v.fileSize),
      name: v.fileName,
      key: v.fileKey,
      url: urlData[v.fileKey],
      ...v,
    };
  });
}
async function init() {
  const paramsNames = `${ParamsNamesType.BASE_ACCOUNT},${ParamsNamesType.MATERIAL_RECEIVING}`;
  const res = await getIsEditParams(paramsNames);
  const isEntryDateEdit = res[ParamsNamesType.BASE_ACCOUNT];
  purchaseTypeLabelOption.value = await getSupplierList();

  entryDateEdit.value = isEntryDateEdit.modifyAccountTime;
}
const jumpTo = (row: any) => {
  router.push({
    name: 'MenuMaterialRequisitionForm',
    query: { code: row.materialName },
  });
};

const prevBtn = () => {
  emit('move', -1);
};
const nextBtn = () => {
  emit('move', 1);
};

onBeforeMount(() => {
  init();
});
</script>

<style lang="scss" scoped>
.el-drawer__header {
  padding: 0 16px;
  margin-bottom: 0;
}

.customer-drawer {
  width: 100% !important;
}

.customer-modal {
  // width: 80% !important;/
  min-width: 1200px;
  margin-left: auto;
}
</style>

<style>
.pointer-events-none {
  z-index: 210 !important;
}
.el-drawer__body {
  padding: 0;
}
.el-form-item--large {
  margin-bottom: 16px;
}
</style>
