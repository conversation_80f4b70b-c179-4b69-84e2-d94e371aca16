<template>
  <div class="selections h-full w-full shadow-sm">
    <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
      <div>退库材料确认</div>
    </div>
    <div class="h-[calc(100%-30px)] w-full">
      <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
        <template #top></template>
        <template #seq="{ $rowIndex }">
          <div>{{ $rowIndex + 1 }}</div>
        </template>
      </VxeGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { vxeBaseConfig } from '#/utils/vxeTool';

const props = withDefaults(
  defineProps<{
    selectionData: any;
  }>(),
  {
    selectionData: [],
  },
);

const emit = defineEmits<{
  (e: 'remove', data: any): void;
}>();

// 表格数据
const tableRef = ref();
// 表格配置
const columns = [
  {
    file: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '150',
  },
  {
    field: 'inventoryQuantity',
    title: '数量',
    width: '150',
  },
  {
    field: 'reversalQuantity',
    title: '退库数量',
    width: '150',
    editRender: {
      name: 'VxeInput',
      props: {
        placeholder: '请输入数量',
      },
    },
  },
];

const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  editConfig: {
    trigger: 'click',
    mode: 'cell',
    showStatus: true,
  },
  columns,
  editRules: {
    reversalQuantity: [
      {
        validator({ cellValue, row }: any) {
          if (cellValue <= 0) {
            return new Error('退库数量必须大于零！');
          } else if (cellValue > row.inventoryQuantity) {
            return new Error(
              `退库数量不能大于库存数量${row.inventoryQuantity}`,
            );
          } else {
            return true;
          }
        },
      },
    ],
  },
  data: [],
});

watch(
  () => props.selectionData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    deep: true,
    immediate: true,
  },
);
// 表格事件
const gridEvents = {
  cellDblclick({ row, rowIndex }: any) {
    if (!row.disabled) {
      emit('remove', { row, rowIndex });
    }
  },
};
</script>
