<template>
  <div class="selections h-full">
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
            <template #type="{ row }">
              <div>
                {{ getMaterialTypeLabel(row.type) }}
              </div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, watch } from 'vue';

import { getMaterialTypeLabel } from '#/types/materialManagement';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});
const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[]; // 已选的明细数据
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionData: () => [],
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'selectAll', data: any): void;

  (e: 'add', data: any): void; // 添加到已选明细的 数据
}>();

const multiple = inject<any>('multiple'); // 是否是多选
// 当前选中的分类表格数据
const currentClassItem = ref();
// 分类表格数据
const prevTableRef = ref();
// 内置分类数据
const staticClassItem = {
  id: '',
  name: '全部',
  parentId: null,
  remark: '',
  type: '',
  disabled: false,
};
// 分类表格配置
const prevColumns = [
  {
    treeNode: true,
    field: 'code',
    title: '编码',
    width: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'name',
    title: '类别名称',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'materialType',
    title: '核算类型',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入单据编码',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
  },
];
const prevTableOptions = reactive<any>({
  ...vxeBaseConfig,
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-100';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns: prevColumns,
  data: props.choiceClassData,
});
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = [staticClassItem, ...nval];
    if (prevTableOptions.data.length > 0) {
      const currentRow = prevTableOptions.data[0];

      setCurrentRow(prevTableOptions.data, prevTableRef.value, currentRow);
      emit('select', currentRow);
    }
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    tableOptions.data = [];
    if (row.disabled) {
      const $grid = prevTableRef.value;
      if ($grid) {
        $grid.clearCurrentRow();
      }
      return; // 如果是禁用状态则不可选
    }
    currentClassItem.value = row;
    emit('select', row);
  },
};

// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    file: 'seq',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'materialName',
    title: '材料名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入材料名称',
      },
    },
  },
  {
    field: 'materialSpec',
    title: '规格型号',
    width: '80',
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'unit',
    title: '计量单位',
    width: '80',
  },
  {
    field: 'inventoryQuantity',
    title: '数量',
    width: '80',
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled) {
      return 'bg-gray-100';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
    // return row.disabled
    //   ? 'bg-gray-100 cursor-not-allowed opacity-70'
    //   : row.selected
    //     ? 'bg-orange-100'
    //     : '';
  },
  columns,
  data: props.choiceDetailData,
});
// 表格数据赋值
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

// 明细表格数据状态修改
const selections = ref(props.selectionData); // 已选数据
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));
    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = multiple.value ? false : !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    if (row.selected || row.disabled) {
      return false;
    }
    emit('add', row);
  },
};
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
