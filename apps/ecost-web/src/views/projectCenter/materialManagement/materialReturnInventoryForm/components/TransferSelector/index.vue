<template>
  <div class="flex h-[540px] flex-col items-center justify-between">
    <!-- 上部确认区 -->
    <div class="h-full w-full">
      <Choices
        :choice-class-data="choiceClass"
        :choice-detail-data="choiceDetail"
        :selection-data="selections"
        @select="choicesSelect"
        @select-all="selectAll"
        @add="choicesChange"
      />
    </div>
    <!-- 中部图标区 -->
    <div class="mt-2 flex items-center justify-center">
      <ElIcon size="26" style="transform: rotate(90deg)">
        <DArrowRight />
      </ElIcon>
    </div>
    <!-- 底部确认区 -->
    <div class="mt-[-20px] h-full w-full">
      <Selections :selection-data="selections" @remove="selectionChange" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { provide, ref, watch } from 'vue';

import { DArrowRight } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';

import Choices from './components/LeftChoices.vue';
import Selections from './components/RightSelections.vue';

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    multiple?: boolean;

    selectionData: any[];
  }>(),
  {
    selectionData: () => [],
    choiceClassData: () => [],
    choiceDetailData: () => [],

    multiple: false,
  },
);

const emit = defineEmits<{
  (e: 'update:selectionData', selectionData: any[]): void;
  (e: 'selectAll', data: any): void;
  (e: 'select', data: any): void;
}>();

const localMultiple = ref(props.multiple);
watch(
  () => props.multiple,
  (nval) => {
    localMultiple.value = nval;
  },
  { deep: true, immediate: true },
);

provide('multiple', localMultiple);

// 选择区:分类
const choiceClass = ref<any[]>([]);
const currentClassItem = ref();
watch(
  () => props.choiceClassData,
  (nval) => {
    choiceClass.value = nval;
  },
  { deep: true, immediate: true },
);
// 选择区:明细
const choiceDetail = ref<any[]>([]);
watch(
  () => props.choiceDetailData,
  (nval) => {
    choiceDetail.value = nval;
  },
  { deep: true, immediate: true },
);

// 已选的数据：分类
// const selectionClass = ref<any[]>([]);
// provide('selectionClass', selectionClass);
// 已选的数据：明细
const selections = ref(props.selectionData);
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
  },
  { deep: true, immediate: true },
);

// 选中分类
async function choicesSelect(row: any) {
  currentClassItem.value = row;
  emit('select', row);
}
// 选中分类并添加全部数据
async function selectAll(row: any) {
  currentClassItem.value = row;
  emit('selectAll', row);
}

// 获取uuid
function generateUuid(): string {
  return Date.now().toString() + Math.floor(Math.random() * 10_000).toString();
}
// 选择器改变
async function choicesChange(row: any) {
  if (localMultiple.value) {
    // 如果多选的加入自定义的_uid
    row._uuid = generateUuid();
    const newSelections = [...selections.value, row];
    selections.value = newSelections;
    emit('update:selectionData', newSelections);
  } else {
    const newSelections = [...selections.value, row];
    selections.value = newSelections;
    emit('update:selectionData', newSelections);
  }
}
// 已选择的数据改变
async function selectionChange({ row, rowIndex }: any) {
  // 如果是多选的情况 使用idx删除
  if (localMultiple.value) {
    selections.value.splice(rowIndex, 1);
    const newSelections = selections.value;
    emit('update:selectionData', newSelections);
  } else {
    const idx = selections.value.findIndex((v) => v.id === row.id);
    if (idx !== -1) {
      const newSelections = selections.value.filter(
        (item) => item.id !== row.id,
      );
      selections.value = newSelections;
      emit('update:selectionData', newSelections);
    }
  }
}
</script>
<style scoped lang="scss"></style>
