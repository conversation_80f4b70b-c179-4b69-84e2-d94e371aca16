<template>
  <div class="selections h-full">
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="h-full">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #index="{ row }">
              <div>{{ row.index }}</div>
            </template>
            <template #contractTemplateType="{ row }">
              <div>
                {{ getContractTemplateTypeLabel(row.contractTemplateType) }}
              </div>
            </template>
            <template #fulfillmentStatus="{ row }">
              <div
                :class="{
                  'text-red-500':
                    row.fulfillmentStatus === FulfillmentStatus.COMPLETED,
                  'text-green-500':
                    row.fulfillmentStatus === FulfillmentStatus.IN_PROGRESS,
                  'text-orange-500':
                    row.fulfillmentStatus === FulfillmentStatus.NOT_STARTED,
                }"
              >
                {{ getFulfillmentStatusLabel(row.fulfillmentStatus) }}
              </div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <div class="nb-1 mt-1 flex items-center justify-center">
        <ElIcon size="26" style="transform: rotate(90deg)">
          <DArrowRight />
        </ElIcon>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <div class="flex h-[30px] items-end justify-between pb-2 text-[14px]">
          <div>选择合同确认</div>
        </div>
        <div class="h-full">
          <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #seq="{ $rowIndex }">
              <div>{{ $rowIndex + 1 }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref, watch } from 'vue';

import {
  ContractTemplateType,
  contractTemplateTypeOptions,
  FulfillmentStatus,
  fulfillmentStatusOptions,
  getContractTemplateTypeLabel,
  getFulfillmentStatusLabel,
  MaterialSettlementStatus,
} from '#/types/materialManagement';
import { setCurrentRow, vxeBaseConfig } from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceContractData: any[];
    chooseContractData: any[];
  }>(),
  {
    choiceContractData: () => [],
    chooseContractData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;

  (e: 'add', data: any): void; // 添加到已选明细的 数据
}>();

const multiple = inject<any>('multiple'); // 是否是多选
// 当前选中的分类表格数据
const currentClassItem = ref();
// 分类表格数据
const prevTableRef = ref();
// 合同数据表格配置
const prevColumns = [
  {
    filed: 'index',
    title: '序号',
    width: '60',
    slots: {
      default: 'index',
    },
  },
  {
    field: 'contractTemplateType',
    title: '合同类型',
    width: '80',
    slots: {
      default: 'contractTemplateType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: contractTemplateTypeOptions, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择合同类型',
      },
    },
  },
  {
    field: 'code',
    title: '合同编号',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入合同编号',
      },
    },
  },
  {
    field: 'name',
    title: '合同名称',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'partyBName',
    title: '供应商名称',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'fulfillmentStatus',
    title: '履约状态',
    width: '100',
    slots: {
      default: 'fulfillmentStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: fulfillmentStatusOptions, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择履约状态',
      },
    },
  },
];
const prevTableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    if (row.disabled || row.fulfillmentStatus === FulfillmentStatus.COMPLETED) {
      return 'bg-gray-100';
    } else if (row.selected) {
      return 'bg-orange-100';
    } else {
      return '';
    }
  },
  columns: prevColumns,
  data: props.choiceContractData,
});
watch(
  () => props.choiceContractData,
  (nval) => {
    if (prevTableOptions.data.length > 0) {
      const currentRow = prevTableOptions.data[0];

      setCurrentRow(prevTableOptions.data, prevTableRef.value, currentRow);
      emit('select', currentRow);
    }
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    if (row.fulfillmentStatus === FulfillmentStatus.COMPLETED) {
      return; // 履约完成的合同不可点击
    }
    tableOptions.data = [];
    if (row.disabled) {
      const $grid = prevTableRef.value;
      if ($grid) {
        $grid.clearCurrentRow();
      }
      return; // 如果是禁用状态则不可选
    }

    currentClassItem.value = row;
  },
  cellDblclick({ row }: any) {
    if (row.fulfillmentStatus === FulfillmentStatus.COMPLETED) {
      return; // 履约完成的合同不可点击
    }
    tableOptions.data = [];
    if (row.disabled || row.selected) {
      const $grid = prevTableRef.value;
      if ($grid) {
        $grid.clearCurrentRow();
      }
      return; // 如果是禁用状态则不可选
    }
    currentClassItem.value = row;
    // row.selected = true;

    emit('select', row);
  },
};

// 确认选择合同表格数据
const tableRef = ref();
// 确认选择合同配置
const columns = [
  {
    file: 'seq',
    width: '60',
    slots: {
      default: 'seq',
    },
  },
  {
    field: 'contractTemplateType',
    title: '合同类型',
    width: '80',
    treeNode: true,
    slots: {
      default: 'contractTemplateType',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: ContractTemplateType, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择合同类型',
      },
    },
  },
  {
    field: 'code',
    title: '合同编号',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入合同编号',
      },
    },
  },
  {
    field: 'name',
    title: '合同名称',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入合同名称',
      },
    },
  },
  {
    field: 'partyBName',
    title: '供应商名称',
    width: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'input',
      props: {
        size: 'mini',
        placeholder: '请输入供应商名称',
      },
    },
  },
  {
    field: 'fulfillmentStatus',
    title: '履约状态',
    width: '100',
    slots: {
      default: 'fulfillmentStatus',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect', // 使用 Element Plus 的下拉组件
      options: MaterialSettlementStatus, // 选项数据
      props: {
        size: 'mini',
        placeholder: '请选择履约状态',
      },
    },
  },
];
const tableOptions = reactive<any>({
  ...vxeBaseConfig,
  rowClassName: ({ row }: any) => {
    return row.disabled
      ? 'bg-gray-100 cursor-not-allowed opacity-70'
      : row.selected
        ? 'bg-orange-100'
        : '';
  },
  columns,
  data: props.chooseContractData,
});
// 表格数据赋值
// watch(
//   () => props.chooseContractData,
//   (nval) => {
//     tableOptions.data = nval;
//   },
//   {
//     immediate: true,
//   },
// );

// 表格数据状态修改
const selections = ref(props.chooseContractData); // 已选数据
watch(
  () => props.chooseContractData,
  (nval) => {
    console.log('props.chooseContractData', selections.value);
    console.log('nval', nval);
    // selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));
    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = multiple.value ? false : !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    // 如果行被禁用，阻止事件传播
    if (row.disabled || row.selected) {
      return false;
    }
    emit('add', row);
  },
};
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
