<template>
  <ColPage v-bind="colPageSettings">
    <!-- 左侧树 -->
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary" size="small">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <div
        :style="{ minWidth: '300px' }"
        v-else
        class="bg-card h-full rounded-lg border p-2"
      >
        <!-- 搜索区域 -->
        <div class="mb-2 mt-2 flex">
          <TreeLevelExpand
            :expand-idx="expandIdx"
            @expand-click="expandClick"
          />
          <ElInput
            class="ml-5 flex-1"
            placeholder="请输入内容"
            v-model="filterText"
          >
            <template #prefix>
              <IconifyIcon icon="ep:search" />
            </template>
          </ElInput>
        </div>
        <div class="tree-content">
          <ElScrollbar height="680px">
            <ElTree
              ref="treeRef"
              class="filter-tree min-w-[360px]"
              :default-expanded-keys="defaultExpendKeys"
              :data="treeData"
              :props="treeDefaultSetting"
              :expand-on-click-node="false"
              default-expand-all
              highlight-current
              :filter-node-method="filterNode"
              :current-node-key="orgId"
              node-key="id"
              @node-click="treeNodeClick"
            >
              <template #default="{ data }">
                <div class="flex w-full items-center justify-between">
                  <div class="item-name w-[140px]">
                    <span>{{ data.name }}</span>
                    <span class="item-nums" v-if="data.children?.length > 0">
                      <span class="color-[orage] in">{{
                        `(${data.children?.length})`
                      }}</span>
                    </span>
                  </div>

                  <div
                    class="item-icon-group flex w-[110px] items-center justify-between pr-[10px]"
                  >
                    <div class="item-icon">
                      <IconifyIcon
                        v-if="data.type === OrgType.TENANT"
                        class="text-l"
                        icon="bi:bank"
                      />
                      <IconifyIcon
                        v-if="data.type === OrgType.COMPANY"
                        class="text-l"
                        icon="bi:building"
                      />
                      <IconifyIcon
                        v-if="data.type === OrgType.PROJECT"
                        class="text-l"
                        icon="bi:house-door"
                      />
                    </div>

                    <div class="item-nums" v-if="data.count">
                      <span>{{ data.count }}个</span>
                    </div>
                    <div clss="item-disable">
                      <IconifyIcon
                        v-if="data.isHide"
                        class="text-l"
                        icon="bi:eye-slash-fill"
                      />
                      <IconifyIcon
                        v-else
                        class="text-l opacity-0"
                        icon="bi:eye-slash-fill"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </ElTree>
          </ElScrollbar>
        </div>
      </div>
    </template>
    <!-- 右侧参数设置 -->
    <div
      class="bg-card flex h-full flex-col rounded-lg border p-2"
      v-loading="isLoading"
    >
      <div class="mt-2 flex-1 overflow-y-auto px-3 pb-3">
        <BasicTitleBar title="账期设置" class="mb-4" />

        <ElFormItem class="mb-4 flex items-center" label="月度账期周期设置：">
          <div class="flex items-center">
            <span>月截至日期</span>
            <ElSelect
              class="ml-1"
              style="width: 80px"
              v-model="monthEnd"
              :disabled="hasBaseAccount"
            >
              <ElOption
                v-for="valueList in enumValueList"
                :key="valueList.value"
                :label="valueList.label"
                :value="valueList.value"
              />
            </ElSelect>
          </div>
        </ElFormItem>

        <ElFormItem class="mb-4 flex items-center" label="年度账期周期设置：">
          <div class="flex items-center">
            <span>月截至日期</span>
            <ElSelect
              class="ml-1"
              style="width: 80px"
              v-model="yearEnd"
              :disabled="hasBaseAccount"
            >
              <ElOption
                v-for="valueList in enumValueList"
                :key="valueList.value"
                :label="valueList.label"
                :value="valueList.value"
              />
            </ElSelect>
          </div>
        </ElFormItem>
        <BasicTitleBar title="进场验收数据补录功能开启设置" class="mb-4" />
        <ElFormItem
          class="mb-4 flex items-center"
          label="是否启用进场验收单补录功能:"
        >
          <ElRadioGroup
            class="ml-4"
            v-model="needHistoryAccount"
            :disabled="isProject"
          >
            <ElRadio :value="true">启用</ElRadio>
            <ElRadio :value="false">关闭</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <BasicTitleBar title="收料单数据来源设置" class="mb-4" />
        <ElFormItem
          class="mb-4 flex items-center"
          label="收料单数据只能来源于进场验收单:"
        >
          <ElRadioGroup
            class="ml-4"
            v-model="isMaterialIncomingInspection"
            :disabled="isProject"
          >
            <ElRadio :value="true">是</ElRadio>
            <ElRadio :value="false">否</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <BasicTitleBar title="基础单据时间是否可修改设置" class="mb-4" />
        <ElFormItem
          class="mb-4 flex items-center"
          label="基础单据时间是否可以修改:"
        >
          <ElRadioGroup
            class="ml-4"
            v-model="modifyAccountTime"
            :disabled="isProject"
          >
            <ElRadio :value="true">是</ElRadio>
            <ElRadio :value="false">否</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
      </div>
    </div>
  </ColPage>
</template>

<script setup lang="ts">
import type { TreeInstance, TreeStoreNodesMap } from 'element-plus';

import type { TreeNode } from '#/types/systemManagement';

import { onBeforeMount, ref, watch } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import {
  ElButton,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElScrollbar,
  ElSelect,
  ElTooltip,
  ElTree,
} from 'element-plus';

import {
  getOrgHasBaseAccount,
  getOrgParams,
  updateOrgParams,
} from '#/api/projectCenter/projectSetting/orgParams';
import { getOrganizationTree } from '#/api/systemManagementApi/organizationApis';
import BasicTitleBar from '#/components/BasicTitleBar/index.vue';
import TreeLevelExpand from '#/components/TreeLevelExpand/index.vue';
import { useCommonStore } from '#/store';
import { OrgType, ParamsName, ParamsType } from '#/types/systemManagement';
import { getIdsByLevel } from '#/utils/common';

const enumValueList = Array.from({ length: 31 }, (_, i) => ({
  label: i + 1,
  value: i + 1,
}));
let ret: any;
const monthEnd = ref();
const yearEnd = ref();
const needHistoryAccount = ref();
const isMaterialIncomingInspection = ref();
const modifyAccountTime = ref();
const isLoading = ref(false);
const hasBaseAccount = ref(false);

const localOrgId = sessionStorage.getItem('x-org-id') || '';
const orgId = ref(localOrgId);

const orgType = sessionStorage.getItem('x-org-type');

const userStore = useUserStore();

// ------ Page页配置 ------
const colPageSettings = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMinWidth: 24,
  leftWidth: 30,
  resizable: true,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

const localIsProject = orgType === 'PROJECT';
const isProject = ref(localIsProject);

if (localIsProject) {
  colPageSettings.value.leftMinWidth = 0;
  colPageSettings.value.leftWidth = 0;

  colPageSettings.value.resizable = false;
}

async function getOrgParamsData() {
  const paramsNames = Object.values(ParamsName).join(',');
  const param = {
    tenantId: userStore.userInfo?.tenantId || '',
    orgId: orgId.value,
    paramsNames,
    paramsType: ParamsType.ECOST,
  };
  const result = await getOrgParams(param);
  ret = result;
  monthEnd.value = result[ParamsName.ACCOUNT_CYCLE].monthEnd;
  yearEnd.value = result[ParamsName.ACCOUNT_CYCLE].yearEnd;

  needHistoryAccount.value =
    result[ParamsName.MATERIAL_INCOMING_INSPECTION].needHistoryAccount;

  isMaterialIncomingInspection.value =
    result[ParamsName.MATERIAL_RECEIVING].isMaterialIncomingInspection;

  modifyAccountTime.value = result[ParamsName.BASE_ACCOUNT].modifyAccountTime;

  const result2 = await getOrgHasBaseAccount({ orgId: orgId.value });
  hasBaseAccount.value = result2.hasBaseAccount;
}

async function updateOrgParamsData(
  paramsName: string,
  fieldName: string,
  val: any,
) {
  if (ret[paramsName][fieldName] === val) {
    return true;
  }
  ret[paramsName][fieldName] = val;
  const param = {
    tenantId: userStore.userInfo?.tenantId || '',
    orgId: orgId.value,
    paramsName,
    params: ret[paramsName],
    paramsType: ParamsType.ECOST,
  };
  await updateOrgParams(param);
  ElMessage.success('操作成功！');
}

getOrgParamsData();

watch(
  [
    monthEnd,
    yearEnd,
    needHistoryAccount,
    isMaterialIncomingInspection,
    modifyAccountTime,
  ],
  ([
    newMonthEnd,
    newYearEnd,
    newNeedHistoryAccount,
    newIsMaterialIncomingInspection,
    newModifyAccountTime,
  ]) => {
    updateOrgParamsData(ParamsName.ACCOUNT_CYCLE, 'monthEnd', newMonthEnd);
    updateOrgParamsData(ParamsName.ACCOUNT_CYCLE, 'yearEnd', newYearEnd);
    updateOrgParamsData(
      ParamsName.MATERIAL_INCOMING_INSPECTION,
      'needHistoryAccount',
      newNeedHistoryAccount,
    );
    updateOrgParamsData(
      ParamsName.MATERIAL_RECEIVING,
      'isMaterialIncomingInspection',
      newIsMaterialIncomingInspection,
    );
    updateOrgParamsData(
      ParamsName.BASE_ACCOUNT,
      'modifyAccountTime',
      newModifyAccountTime,
    );
  },
);

// 左侧组织树相关
const treeRef = ref<TreeInstance>();
const defaultExpendKeys = ref<string[]>([]);
const treeData = ref([]);

// 展开层级下标
const expandIdx = ref(0);
// 筛选数据
const filterText = ref('');
const filterNode = (value: string, data: any) => {
  // 筛选方法
  if (!value) return true;
  return data.name.includes(value);
};

//  ------ Tree数据 ------
const treeDefaultSetting = ref({
  value: 'id',
  label: 'name',
  disabled: (v: any) => !v.isDirect,
  children: 'children',
});

watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const expandClick = (level: number) => {
  // 展开前提前关闭所有节点
  if (!treeRef.value) return;
  const nodeDatas: TreeStoreNodesMap = treeRef.value.store.nodesMap;

  for (const key in nodeDatas) {
    if (nodeDatas[key]) {
      nodeDatas[key].expanded = false;
    }
  }
  // 点击展开方法
  defaultExpendKeys.value = getIdsByLevel(treeData.value, level);
  expandIdx.value = level;
};

// 获取组织树接口
async function getOrgList() {
  const tenantId = userStore.userInfo?.tenantId || '';
  const params = { tenantId };
  const res = await getOrganizationTree(params);
  calculateCount(res);
  treeData.value = res;
}

const calculateCount = (nodes: any[]): number => {
  let total = 0;
  nodes.forEach((node) => {
    // 如果有子节点，递归计算子节点的 count
    if (node.children && node.children.length > 0) {
      node.count = calculateCount(node.children);
    }
    // 如果当前节点是 'project' 类型，累加到 total
    if (node.type === 'PROJECT') {
      total += 1;
    }
    // 累加子节点的 count
    if (node.count) {
      total += node.count;
    }
  });
  return total;
};

const curTreeItem = ref<TreeNode>({
  // 当前左键选中的树的数据
  id: '',
  name: '',
  type: OrgType.COMPANY,
});
const treeNodeClick = async (data: TreeNode) => {
  curTreeItem.value = data;
  orgId.value = data.id;
  isLoading.value = true;
  await getOrgParamsData();
  isLoading.value = false;
};

const commonStore = useCommonStore();
async function init() {
  commonStore.getOrganizationTreeData();
  commonStore.getRoleTreeData();

  await getOrgList();
}

onBeforeMount(async () => {
  await init();
});
</script>

<style scoped lang="scss">
.tree-content {
  height: calc(100vh - 180px);
  overflow: auto;
}
</style>
