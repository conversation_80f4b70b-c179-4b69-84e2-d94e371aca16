<template>
  <div class="org-selector align-center flex w-full">
    <ElTreeSelect
      v-bind="$attrs"
      ref="treeSelectRef"
      v-model="orgId"
      :data="treeData"
      :props="{
        label: isSealName ? 'sealName' : 'name',
        disabled: (v: TreeData) =>
          (disabledFiled !== null && v.type === disabledFiled) ||
          (localDisabledSelf && v.id === currentOrgId),
      }"
      :placeholder="placeholder"
      :disabled="disabled"
      :default-expand-all="defaultExpandall"
      :multiple="multiple"
      :show-checkbox="multiple"
      :check-on-click-node="multiple"
      filterable
      :clearable="clearable"
      check-strictly
      :filter-node-method="filterNode"
      node-key="id"
      :collapse-tags="true"
      collapse-tags-tooltip
      :max-collapse-tags="2"
      @remove-tag="handleRemoveClick"
      @check="handleCheckClick"
      @node-click="handleNodeClick"
    >
      <!-- 自定义当前选中值的展示 -->
      <template #label v-if="displayLabel">
        <span>{{ displayLabel }}</span>
      </template>
    </ElTreeSelect>
  </div>
</template>
<!-- 组织选择器 -->
<script lang="ts" setup>
import type { TreeData } from './index.interface';

import { ref, watch } from 'vue';

import { ElTreeSelect } from 'element-plus';
import _ from 'lodash';

import { useCommonStore } from '#/store';

import { OrgType } from './index.interface';

interface Props {
  modelValue?: null | string | string[];
  disabledProps?: null | OrgType;
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  defaultExpandall?: boolean;
  disabledSelf?: boolean; // 是否禁用自身
  isSealName?: boolean; // 是否需要全称

  displayLabel?: null | string;
}

defineOptions({
  name: 'OrgSelector',
});
const props = withDefaults(defineProps<Props>(), {
  disabledProps: null,
  modelValue: null,
  placeholder: '请选择组织',
  disabled: false,
  clearable: false,
  multiple: false,
  defaultExpandall: true,
  disabledSelf: false,
  isSealName: false, // 是否需要全称

  displayLabel: null, // 展示的标签
});

const emit = defineEmits(['update:modelValue', 'change']);

const commonStore = useCommonStore();

interface Tree {
  [key: string]: any;
}
const orgId = ref<null | string | string[]>(null);
const disabledFiled = ref<null | OrgType>(null); // 禁用的类型
const treeData = ref(commonStore.organizationTree);
const localDisabledSelf = ref(props.disabledSelf);
const currentOrgId = sessionStorage.getItem('x-org-id');

// 因为数据不同步的问题，这样处理
watch(
  () => commonStore.organizationTree,
  (nval) => {
    treeData.value = nval;
  },
);

const treeSelectRef = ref();
// 监听外部值变化
watch(
  () => props.modelValue,
  (val) => {
    orgId.value = val;
  },
  { immediate: true },
);
// 监听外部值变化
watch(
  () => props.disabledProps,
  (nval) => {
    disabledFiled.value = nval;
  },
  { immediate: true },
);
// 监听内部值变化
watch(
  () => orgId.value,
  (newVal, oldVal) => {
    emit('update:modelValue', newVal);
  },
);

function handleCheckClick() {
  const oldVal = _.cloneDeep(orgId.value);
  setTimeout(() => {
    const newVal = orgId.value;
    if (newVal?.length === 0) {
      orgId.value = oldVal;
    } else {
      emit('change', orgId.value);
    }
  }, 0);
}

function handleNodeClick() {
  if (props.multiple) {
    return;
  }

  const oldVal = _.cloneDeep(orgId.value);
  setTimeout(() => {
    const newVal = orgId.value;
    if (newVal?.length === 0) {
      orgId.value = oldVal;
    } else {
      emit('change', orgId.value);
    }
  }, 0);
}

function handleRemoveClick(item: any) {
  const newVal = orgId.value;
  if (newVal?.length === 0) {
    orgId.value = [item];
  } else {
    setTimeout(() => {
      emit('change', orgId.value);
    }, 0);
  }
}

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.name.includes(value);
};
</script>

<style lang="scss" scoped>
.org-selector {
  display: inline-block;
}

.custom-tree-select .el-select-dropdown {
  height: 40px;
  /* 固定下拉框高度 */
  overflow-y: auto;
  /* 如果内容超过高度，显示滚动条 */
}
</style>
