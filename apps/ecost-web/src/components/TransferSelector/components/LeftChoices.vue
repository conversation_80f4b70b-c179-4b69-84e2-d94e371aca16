<template>
  <div class="selections h-full">
    <div class="flex h-[30px] justify-between text-[14px]">
      <ElRadioGroup v-model="curTab">
        <ElRadio
          v-for="v in tabOptions"
          :key="v.value"
          :value="v.value"
          :disabled="v.disabled"
          size="small"
        >
          {{ v.label }}
        </ElRadio>
        <!-- <ElRadio value="ContractMaterial" size="small">选择合同材料</ElRadio> -->
        <!-- <ElRadio value="MaterialMasterPlan" size="small" disabled>
          选择材料总计划
        </ElRadio> -->
      </ElRadioGroup>
    </div>
    <div class="flex h-[calc(100%-30px)] w-full justify-between">
      <!-- 分类 -->
      <div class="h-full w-[48%]">
        <div class="item-center mb-[8px] flex h-[30px] gap-6">
          <!-- <ElInput
            placeholder="搜索材料类别"
            v-model="prevfilterText"
            size="small"
            @keydown.enter="classHandleSearch"
          >
            <template #prefix>
              <IconifyIcon icon="ep:search" />
            </template>
          </ElInput> -->
          <TreeLevelExpand
            :expand-idx="expandIdx"
            @expand-click="expandClick"
          />
        </div>
        <div class="h-[calc(100%-38px)]">
          <VxeGrid
            ref="prevTableRef"
            v-bind="prevTableOptions"
            v-on="prevGridEvents"
          >
            <template #type="{ row }">
              <div>{{ getMaterialTypeLabel(row.type) }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
      <!-- 明细 -->
      <div class="h-full w-[48%]">
        <!-- <div class="item-center mb-[8px] flex h-[30px]">
          <ElInput
            placeholder="搜索材料明细"
            v-model="filterText"
            size="small"
            @keydown.enter="detailHandleSearch"
          >
            <template #prefix>
              <IconifyIcon icon="ep:search" />
            </template>
          </ElInput>
        </div> -->

        <!-- <div class="h-[calc(100%-38px)]"> -->
        <div class="h-[100%]">
          <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="gridEvents">
            <template #type="{ row }">
              <div>{{ getMaterialTypeLabel(row.type) }}</div>
            </template>
          </VxeGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive, ref, watch } from 'vue';

import { ElRadio, ElRadioGroup } from 'element-plus';

import TreeLevelExpand from '#/components/TreeLevelExpand/index.vue';
import {
  getMaterialTypeLabel,
  materialTypeLabelOption,
} from '#/types/materialManagement';
import { flattenTreeToLevel } from '#/utils/vxeTool';

defineOptions({
  name: 'Choices',
});

const props = withDefaults(
  defineProps<{
    choiceClassData: any[];
    choiceDetailData: any[];
    selectionData: any[];
  }>(),
  {
    choiceClassData: () => [],
    choiceDetailData: () => [],
    selectionData: () => [],
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'select', data: any): void;
  (e: 'add', data: any): void;
  (e: 'classSearch', text: any): void;
  (e: 'deatilSearch', text: any): void;
}>();

const curTab = inject<any>('curTab');
// tab项
const tabOptions = ref([
  { label: '选择材料字典', value: 'MATERIAL_DICT', disabled: true },
  { label: '选择合同材料', value: 'CONTRACT', disabled: true },
  // { label: '选择材料总计划', value: 'MaterialMasterPlan', disabled: true },
]);
tabOptions.value.forEach((item) => {
  item.disabled = item.value !== curTab.value;
});

// 展开层级下标
const expandIdx = ref(0);
// 筛选数据
const prevfilterText = ref('');
const filterText = ref('');
// 当前选中的分类表格数据
const currentClassItem = ref({
  id: '',
});
// 分类表格数据
const prevTableRef = ref();
// 内置分类数据
const staticClassItem = {
  id: '',
  name: '全部',
  parentId: null,
  remark: '',
  type: '',
  disabled: true,
};
// 分类表格配置
const prevColumns = [
  {
    field: 'code',
    title: '编码',
    width: '80',
    treeNode: true,
  },
  {
    field: 'name',
    title: '类别名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入类别名称',
      },
    },
  },
  {
    field: 'type',
    title: '核算类型',
    width: '100',
    slots: {
      default: 'type',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect',
      options: materialTypeLabelOption,
      props: {
        size: 'mini',
        placeholder: '请输入核算类型',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '80',
  },
];
const prevTableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  rowClassName: ({ row }: any) => {
    return row.disabled ? 'bg-gray-100' : '';
  },
  loading: false,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: prevColumns,
  data: [],
});
watch(
  () => props.choiceClassData,
  (nval) => {
    prevTableOptions.data = [staticClassItem, ...nval];
  },
  { immediate: true },
);
// 表格事件
const prevGridEvents = {
  cellClick({ row }: { row: any }) {
    if (row.id === currentClassItem.value.id) return;
    currentClassItem.value = row;
    emit('select', row);
  },
};
// 明细表格数据
const tableRef = ref();
// 明细表格配置
const columns = [
  {
    type: 'seq',
    field: 'seq',
    title: ' ',
    width: '60',
  },
  {
    field: 'code',
    title: '编码',
    width: '80',
  },
  {
    field: 'name',
    title: '名称',
    minWidth: '100',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入名称',
      },
    },
  },
  {
    field: 'specificationModel',
    title: '规格型号',
    width: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入规格型号',
      },
    },
  },
  {
    field: 'type',
    title: '核算类型',
    width: '100',
    slots: {
      default: 'type',
    },
    filters: [{ data: '' }],
    filterRender: {
      name: 'VxeSelect',
      options: materialTypeLabelOption,
      props: {
        size: 'mini',
        placeholder: '请输入核算类型',
      },
    },
  },
  {
    field: 'meteringUnit',
    title: '计量单位',
    width: '80',
    filters: [{ data: '' }],
    filterRender: {
      name: 'FilterInput',
      props: {
        size: 'mini',
        placeholder: '请输入计量单位',
      },
    },
  },
  {
    field: 'remark',
    title: '备注',
    width: '80',
  },
];
const tableOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-sky-200 text-black-800',
  loading: false,
  virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  rowClassName: ({ row }: any) => {
    return row.selected ? 'bg-orange-100' : '';
  },
  columnConfig: {
    resizable: true,
  },
  mouseConfig: {
    selected: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns,
  data: [],
});
// 表格数据赋值
watch(
  () => props.choiceDetailData,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
  },
);

const multiple = inject('multiple');

// 表格数据状态修改
const selections = ref(props.selectionData);
watch(
  () => props.selectionData,
  (nval) => {
    selections.value = nval;
    const ids = new Set(selections.value.map((item) => item.id));
    tableOptions.data.forEach((item: any) => {
      const id = item.id;
      const selected = multiple.value ? false : !!ids.has(id);
      item.selected = selected;
    });
  },
  {
    immediate: true,
  },
);

// 明细表格事件
const gridEvents = {
  cellDblclick({ row }: any) {
    if (!row.selected) {
      emit('add', row);
    }
  },
};

// 表格展开
const expandClick = (level: number) => {
  expandIdx.value = level;
  const $grid = prevTableRef.value;
  $grid.clearTreeExpand();
  if (level === 0) {
    $grid.setAllTreeExpand(true);
  } else {
    const tableData = $grid.getTableData();
    const targetData = flattenTreeToLevel(tableData.fullData, level);
    $grid.setTreeExpand(targetData, true);
  }
};

function classHandleSearch() {
  emit('classSearch', prevfilterText.value);
}

function detailHandleSearch() {
  emit('deatilSearch', filterText.value);
}

function init() {
  if (prevTableOptions.data.length > 0) {
    const currentRow = prevTableOptions.data[0];
    emit('select', currentRow);
    setCurrentRow(currentRow.id);
  }
}

async function setCurrentRow(
  value: string,
  key: string = 'id',
  isExpand: boolean = true,
) {
  const activeRow = prevTableOptions.data.find((v: any) => v[key] === value);
  const $grid = prevTableRef.value;
  nextTick(() => {
    if (activeRow) {
      $grid.setCurrentRow(activeRow);
      currentClassItem.value = activeRow;
    }
    $grid.setAllTreeExpand(isExpand);
  });
}

onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.el-radio {
  margin-right: 20px;
}
</style>
